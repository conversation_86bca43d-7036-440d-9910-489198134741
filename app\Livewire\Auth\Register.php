<?php

namespace App\Livewire\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;

class Register extends Component
{
    public $name;
    public $email;
    public $password;
    public $password_confirmation;
    public ?int $clubId = null;
    public ?int $lastClubId = null;

    protected $rules = [
        'name' => ['required', 'string', 'max:255', 'unique:players,client_nick'], 
        'email' => ['required', 'string', 'email', 'max:255', 'unique:players,email'],
        'password' => ['required', 'string', 'min:8', 'confirmed'],
        'clubId' => 'required|integer|min:1'
    ];

    protected $messages = [
        'name.required' => 'Пожалуйста, введите имя пользователя',
        'name.string' => 'Имя должно быть текстом',
        'name.max' => 'Имя не должно превышать 255 символов',
        'name.unique' => 'Это имя пользователя уже занято',
        'email.required' => 'Пожалуйста, введите email',
        'email.string' => 'Email должен быть текстом',
        'email.email' => 'Пожалуйста, введите корректный email',
        'email.max' => 'Email не должен превышать 255 символов',
        'email.unique' => 'Этот email уже зарегистрирован',
        'password.required' => 'Пожалуйста, введите пароль',
        'password.string' => 'Пароль должен быть текстом',
        'password.min' => 'Пароль должен содержать минимум 8 символов',
        'password.confirmed' => 'Пароли не совпадают',
        'clubId.required' => 'ID клуба обязателен',
        'clubId.integer' => 'ID клуба должен быть числом',
        'clubId.min' => 'ID клуба должен быть больше 0'
    ];

    public function mount()
    {
        // Пытаемся получить clubId из cookie
        $this->clubId = Cookie::get('last_club_id');

        // Если нет в cookie, получаем из БД
        if (!$this->clubId) {
            $this->lastClubId = DB::table('players')
                ->whereNotNull('club_id')
                ->latest()
                ->value('club_id');
        }
    }

    public function useLastClubId()
    {
        $this->clubId = $this->lastClubId;
    }

    public function register() {
        $this->validate([
            'name' => ['required', 'string', 'max:255', 'unique:players,client_nick'], 
            'email' => ['required', 'string', 'email', 'max:255', 'unique:players,email'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ], [
            'name.required' => 'Пожалуйста, введите имя пользователя',
            'name.string' => 'Имя должно быть текстом',
            'name.max' => 'Имя не должно превышать 255 символов',
            'name.unique' => 'Это имя пользователя уже занято',
            'email.required' => 'Пожалуйста, введите email',
            'email.string' => 'Email должен быть текстом',
            'email.email' => 'Пожалуйста, введите корректный email',
            'email.max' => 'Email не должен превышать 255 символов',
            'email.unique' => 'Этот email уже зарегистрирован',
            'password.required' => 'Пожалуйста, введите пароль',
            'password.string' => 'Пароль должен быть текстом',
            'password.min' => 'Пароль должен содержать минимум 8 символов',
            'password.confirmed' => 'Пароли не совпадают',
        ]);

        // Проверяем, что client_nick или email заполнены
        if (empty($this->name) && empty($this->email)) {
            $this->addError('client_nick', 'Необходимо указать имя пользователя или email');
            return;
        }
        //Pa$$w0rd!
        $user = User::create([
            'client_nick' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password), // Хешируем пароль
            'club_id' => 1,
        ]);


        Auth::login($user);

        // После успешной регистрации сохраняем clubId в cookie
        Cookie::queue('last_club_id', $this->clubId, 60 * 24 * 30); // 30 дней

        return redirect()->intended('/dashboard');
    }

    public function render()
    {
        return view('livewire.auth.register');
    }
}









