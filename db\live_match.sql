DELIMITER //

CREATE PROCEDURE add_columns_live_match()
BEGIN
    -- Проверяем существование столбца selected_map_id
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'live_match' 
        AND COLUMN_NAME = 'selected_map_id'
    ) THEN
        ALTER TABLE `live_match`
        ADD COLUMN `selected_map_id` BIGINT UNSIGNED NULL DEFAULT NULL AFTER `status`;
        
        -- Добавляем внешний ключ только если столбец был создан
        ALTER TABLE `live_match`
        ADD CONSTRAINT `fk_live_match_selected_map` 
        FOREIGN KEY (`selected_map_id`) 
        REFERENCES `cs2_maps` (`id`) 
        ON DELETE SET NULL;
    END IF;
    
    -- Проверяем существование столбца current_voting_team
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'live_match' 
        AND COLUMN_NAME = 'current_voting_team'
    ) THEN
        ALTER TABLE `live_match`
        ADD COLUMN `current_voting_team` INT(11) NULL DEFAULT NULL AFTER `math_score`;
    END IF;
END //

DELIMITER ;

-- Вызываем процедуру
CALL add_columns_live_match();

-- Удаляем процедуру после использования
DROP PROCEDURE IF EXISTS add_columns_live_match;