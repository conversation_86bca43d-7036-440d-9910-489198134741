<?php

namespace App\Livewire;

use App\Enums\CurrentVoter;
use App\Enums\MatchStatus;
use App\Models\Cs2Map;
use App\Models\LiveMatch;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Livewire\Component;

class FindMatchMapVoting extends Component
{
    public $matchId;
    public $teamId;
    public $foundMatch;
    public $availableMaps = [];
    public $availableMapsById = [];
    public $bannedMaps = [];
    public $currentVotingTeam = null;

    public function mount($matchId, $teamId)
    {
        $this->matchId = $matchId;
        $this->teamId = $teamId;
        $this->foundMatch = LiveMatch::find($matchId);
        $this->loadAvailableMaps();
        $this->loadBannedMapsFromDatabase();
        $this->currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();
    }

    protected function loadAvailableMaps()
    {
        // Загружаем карты в зависимости от типа игры
        if ($this->foundMatch && $this->foundMatch->team1->game_id) {
            switch ($this->foundMatch->team1->game_id) {
                case 1: // CS2
                    $this->availableMaps = Cs2Map::all()->toArray();
                    break;
                case 2: // Dota 2
                    $this->availableMaps = [
                        ['id' => 1, 'name' => 'Default Map'],
                    ];
                    break;
                case 3: // PUBG
                    $this->availableMaps = [
                        ['id' => 1, 'name' => 'Erangel'],
                        ['id' => 2, 'name' => 'Miramar'],
                        ['id' => 3, 'name' => 'Sanhok'],
                        ['id' => 4, 'name' => 'Vikendi'],
                        ['id' => 5, 'name' => 'Karakin'],
                        ['id' => 6, 'name' => 'Paramo'],
                        ['id' => 7, 'name' => 'Haven'],
                    ];
                    break;
                default:
                    $this->availableMaps = [];
            }
        } else {
            $this->availableMaps = [];
        }

       // Создаем ассоциативный массив для быстрого доступа к картам по ID
       if (! empty($this->availableMaps)) {
           $this->availableMapsById = collect($this->availableMaps)->keyBy('id')->toArray();
       }
   }

    protected function loadBannedMapsFromDatabase()
    {
        if (!$this->foundMatch) return;

        try {
            $bannedMaps = DB::table('live_match_banned_maps')
                ->where('match_id', $this->foundMatch->id)
                ->get();

            if (! empty($bannedMaps)) {
                $this->bannedMaps = $bannedMaps->pluck('map_id')->toArray();
            }
        } catch (\Exception $e) {
            // Log the error for debugging purposes
            \Illuminate\Support\Facades\Log::error('Ошибка при загрузке забаненных карт: '.$e->getMessage());
        }
    }

    public function banMap($mapId)
    {
        if (!$this->foundMatch) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        // Проверяем, чей сейчас ход
        $currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();

        if (!$currentVotingTeam || (int)$currentVotingTeam->id !== (int)$this->teamId) {
            $this->addError('map', 'Сейчас не ваш ход');
            return;
        }

        // Проверяем, что карта еще не забанена
        if (in_array($mapId, $this->bannedMaps)) {
            $this->addError('map', 'Эта карта уже забанена');
            return;
        }

        // Проверяем, что осталось больше одной карты, если это не последняя карта для бана
        if (count($this->availableMaps) - count($this->bannedMaps) <= 1) {
            $this->addError('map', 'Должна остаться хотя бы одна карта');
            return;
        }

        try {
            DB::beginTransaction();

            // Добавляем карту в список забаненных
            $this->bannedMaps[] = $mapId;

            // Пытаемся сохранить бан карты в базе данных, если таблица существует
            try {
                if (Schema::hasTable('live_match_banned_maps')) {
                    DB::table('live_match_banned_maps')->insert([
                        'match_id' => $this->foundMatch->id,
                        'map_id' => $mapId,
                        'team_id' => $this->teamId,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            } catch (\Exception $e) {
                // Log the error for debugging purposes
                \Illuminate\Support\Facades\Log::error('Ошибка при сохранении бана карты в базе данных: ' . $e->getMessage());
                $this->addError('map', 'Ошибка при сохранении бана карты в базе данных: ' . $e->getMessage());
            }

            // Переключаем ход на другую команду
            $this->foundMatch->switchVoter();
            $this->currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();

            // Если осталась одна карта, начинаем матч
            if (count($this->availableMaps) - count($this->bannedMaps) === 1) {
                // Находим оставшуюся карту
                $remainingMap = collect($this->availableMaps)
                    ->filter(function($map) {
                        return !in_array($map['id'], $this->bannedMaps);
                    })
                    ->first();

                if ($remainingMap) {
                    // Обновляем статус матча и выбранную карту
                    DB::table('live_match')
                        ->where('id', $this->foundMatch->id)
                        ->update([
                            'status' => MatchStatus::LIVE->value,
                            'selected_map_id' => $remainingMap['id']
                        ]);

                    // Отправляем событие о начале матча
                    \Illuminate\Support\Facades\Log::info('Отправка события MatchStarted', ['match_id' => $this->foundMatch->id, 'map_id' => $remainingMap['id']]);
                    event(new \App\Events\MatchStarted($this->foundMatch->id, $remainingMap['id']));

                    session()->flash('success', 'Матч начинается на карте: ' . $remainingMap['name']);
                }
            }

            DB::commit();

            // Отправляем событие о бане карты с полным списком забаненных карт

            // Отправляем событие о переключении голосующего
            \Illuminate\Support\Facades\Log::info('Отправка события VoterSwitched', ['match_id' => $this->foundMatch->id, 'current_voter' => $this->foundMatch->current_voter]);
            event(new \App\Events\VoterSwitched($this->foundMatch->id, $this->foundMatch->current_voter));

            // Обновляем компонент
            $this->dispatch('mapBanned', ['mapId' => $mapId]);

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Ошибка при бане карты: ' . $e->getMessage());
        }
    }

    public function unbanMap($mapId)
    {
        if (!$this->foundMatch) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        // Проверяем, чей сейчас ход
        $currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();
        if (!$currentVotingTeam || (int)$currentVotingTeam->id !== (int)$this->teamId) {
            $this->addError('map', 'Сейчас не ваш ход');
            return;
        }

        // Проверяем, забанена ли карта
        if (!in_array($mapId, $this->bannedMaps)) {
            $this->addError('map', 'Эта карта не забанена');
            return;
        }

        try {
            DB::beginTransaction();

            // Удаляем карту из списка забаненных
            $this->bannedMaps = array_values(array_filter($this->bannedMaps, function($id) use ($mapId) {
                return $id != $mapId;
            }));

            // Удаляем запись из базы данных
            try {
                if (Schema::hasTable('live_match_banned_maps')) {
                    DB::table('live_match_banned_maps')
                        ->where('match_id', $this->foundMatch->id)
                        ->where('map_id', $mapId)
                        ->delete();
                }
            } catch (\Exception $e) {
                // Log the error for debugging purposes
                \Illuminate\Support\Facades\Log::error('Ошибка при удалении бана карты из базы данных: ' . $e->getMessage());
                $this->addError('map', 'Ошибка при удалении бана карты из базы данных: ' . $e->getMessage());
            }

            DB::commit();

            // Отправляем событие о разбане карты
            event(new \App\Events\MapUnbanned($this->foundMatch->id, $mapId, $this->teamId, $this->bannedMaps));

            // Обновляем компонент
            $this->dispatch('mapUnbanned', ['mapId' => $mapId]);

            session()->flash('success', 'Бан карты снят');

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Ошибка при снятии бана карты: ' . $e->getMessage());
        }
    }

    public function refreshVotingTeam()
    {
        if ($this->foundMatch) {
            $this->currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();
        }
    }

    public function syncBannedMaps()
    {
        if (!$this->foundMatch) {
            return;
        }

        try {
            if (Schema::hasTable('live_match_banned_maps')) {
                $bannedMaps = DB::table('live_match_banned_maps')
                    ->where('match_id', $this->foundMatch->id)
                    ->pluck('map_id')
                    ->toArray();

                // Обновляем список забаненных карт
                $this->bannedMaps = $bannedMaps;
            }

            // Обновляем текущую голосующую команду
            $this->currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();
        } catch (\Exception $e) {
            // Упрощенное логирование только в режиме отладки
            if (config('app.debug')) {
                \Illuminate\Support\Facades\Log::warning('Ошибка синхронизации данных: '.$e->getMessage());
            }
        }
    }

    public function render()
    {
        return view('livewire.find-match-map-voting');
    }
}