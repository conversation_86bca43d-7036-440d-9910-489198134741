<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\TeamInvitation;

class TeamInvitationCancelled implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $invitationId;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(int $invitationId)
    {
        $this->invitationId = $invitationId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        $invitation = TeamInvitation::find($this->invitationId);
        if (!$invitation) {
            return new PrivateChannel('team.error'); // Или другой канал для ошибок
        }
        return new Channel('team.' . $invitation->team_id);
    }

    public function broadcastAs()
    {
        return 'team.invitation.cancelled';
    }
}