<?php

use App\Models\GameMatch;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Events\MatchFound;

Route::get('/test-event', function () {
    event(new MatchFound('Test message'));
    return 'Event dispatched!';
});

Route::get('/', App\Livewire\Home::class)->name('home');

Route::get('/login', App\Livewire\Auth\Login::class)->name('login');
Route::get('/register', App\Livewire\Auth\Register::class)->name('register');
Route::get('/forgot-password', App\Livewire\Auth\ForgotPassword::class)->name('password.request');
Route::get('/reset-password/{token}', App\Livewire\Auth\ResetPassword::class)->name('password.reset');

// Защищенные маршруты
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', App\Livewire\Dashboard::class)->name('dashboard');
    Route::post('/logout', function () {
        Auth::logout();
        request()->session()->invalidate();
        request()->session()->regenerateToken();
        return redirect('/login');
    })->name('logout');

    Route::get('profile', App\Livewire\Auth\Profile::class)->name('profile');
    Route::get('team', App\Livewire\Team::class)->name('team');
    Route::get('create/team', App\Livewire\CreateTeam::class)->name('create.team');
    Route::get('/find-team/{gameId?}', \App\Livewire\FindTeam::class)->name('find.team');

    // Маршрут для управления токенами
    Route::get('tokens', App\Livewire\Auth\TokenManager::class)->name('tokens');

    // Маршруты для MatchController
    Route::post('/match/{gameType}/start', [MatchController::class, 'startMatch']);
    Route::post('/match/{gameType}/result', [MatchController::class, 'addMatchResult']);

    // Маршрут для отображения результатов матчей через Livewire
    Route::get('matches', App\Livewire\MatchResults::class)->name('matches');
    
    // Новый маршрут для отображения матчей через новый Livewire компонент
    Route::get('game-matches', App\Livewire\Matches::class)->name('game-matches');

    Route::get('/rating/{type?}', App\Livewire\Rating::class)->name('rating');

    Route::get('/player/{player}', App\Livewire\Player\Show::class)->name('player.show');

    Route::get('/club/{club}', App\Livewire\Club\Show::class)->name('club.show');

    Route::get('/match/{match}/ban-maps', App\Livewire\MapBan::class)->name('match.ban-maps');

    // Маршрут для поиска матча
    Route::get('/find-match/{teamId?}', \App\Livewire\FindMatch::class)->name('find.match');
    // Route::get('find-match-pusher/{teamId?}', \App\Livewire\FindMatchWithPusher::class)->name('find.match.pusher');

    Route::post('/broadcasting/auth', [\Illuminate\Broadcasting\BroadcastController::class, 'authenticate'])->middleware(['auth']);
});

Route::get('/test-pusher/{teamId}', function ($teamId) {
    event(new \App\Events\TeamMemberJoined($teamId));
    return "Событие отправлено для команды $teamId";
});

// Маршрут для авторизации по токену
Route::get('auth-token', function (Request $request) {
    if (Auth::check()) {
        return redirect()->route('team');
    } else {
        return redirect()->route('login');
    }
    return abort(404); // Эта строка никогда не будет достигнута из-за предыдущих редиректов
})->middleware('auth.token');

// Тестовые маршруты для уведомлений
Route::get('/test-notifications', function () {
    return view('test-notifications');
})->name('test.notifications');

Route::get('/notification-guide', function () {
    return view('notification-guide');
})->name('notification.guide');

Route::get('/test-pusher', function () {
    return view('test-pusher');
})->name('test.pusher');

Route::get('/simple-pusher-test', function () {
    return view('simple-pusher-test');
})->name('simple.pusher.test');

Route::get('/test-match-notifications', function () {
    return view('test-match-notifications');
})->name('test.match.notifications');

Route::post('/test-simple-pusher', function (Request $request) {
    // Отправляем простое событие
    broadcast(new \Illuminate\Broadcasting\BroadcastEvent([
        'channel' => 'test-channel',
        'event' => 'test-event',
        'data' => $request->all()
    ]));

    return response()->json(['status' => 'success', 'message' => 'Event sent']);
});

Route::post('/test-event/team-accepted', function (Request $request) {
    event(new \App\Events\TeamAcceptedMatch($request->match_id, $request->team_id));
    return response()->json(['status' => 'success', 'message' => 'Team accepted event sent']);
});

Route::post('/test-event/match-declined', function (Request $request) {
    event(new \App\Events\MatchDeclined($request->match_id, $request->team_id));
    return response()->json(['status' => 'success', 'message' => 'Match declined event sent']);
});

Route::post('/test-event/map-banned', function (Request $request) {
    event(new \App\Events\MapBanned($request->match_id, $request->map_id, $request->team_id, $request->banned_maps ?? []));
    return response()->json(['status' => 'success', 'message' => 'Map banned event sent']);
});

Route::post('/test-event/voter-switched', function (Request $request) {
    event(new \App\Events\VoterSwitched($request->match_id, $request->current_voter));
    return response()->json(['status' => 'success', 'message' => 'Voter switched event sent']);
});

// AJAX маршрут для проверки обновлений матча
Route::post('/match/check-updates/{teamId}', function ($teamId) {
    // Создаем временный экземпляр компонента для проверки обновлений
    $component = new \App\Livewire\FindMatch();
    $component->mount($teamId);

    return $component->checkMatchUpdates();
})->name('match.check.updates');























