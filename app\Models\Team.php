<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;
    
    protected $fillable = ['name', 'game_id', 'captain_id', 'rating'];

    public function game(): BelongsTo
    {
        return $this->belongsTo(Game::class);
    }

    public function captain(): BelongsT<PERSON>
    {
        return $this->belongsTo(User::class, 'captain_id');
    }

    public function members(): HasMany
    {
        return $this->hasMany(TeamMember::class);
    }

    public function invitations(): HasMany
    {
        return $this->hasMany(TeamInvitation::class)->where('status', 'pending');
    }

    public function requests(): HasMany
    {
        return $this->hasMany(JoinRequest::class);
    }

    // Проверить, является ли пользователь членом команды
    public function hasMember($userId)
    {
        return $this->members()->where('player_id', $userId)->exists();
    }

    // Получить роль пользователя в команде
    public function getMemberRole($userId)
    {
        $member = $this->members()->where('player_id', $userId)->first();
        return $member ? $member->role : null;
    }

    // Получить всех участников команды с их данными
    public function getMembersWithDetails()
    {
        return $this->members()->with('user')->get();
    }

    // Получить все активные приглашения команды
    public function getPendingInvitations()
    {
        return $this->invitations()->where('status', 'pending')->with('user')->get();
    }

    // Получить все активные заявки команды
    public function getPendingRequests()
    {
        return $this->requests()->where('status', 'pending')->with('user')->get();
    }

    // Проверить, есть ли у команды активные приглашения для пользователя
    public function hasPendingInvitationFor($userId)
    {
        return $this->invitations()
            ->where('player_id', $userId)
            ->where('status', 'pending')
            ->exists();
    }

    // Проверить, есть ли у команды активные заявки от пользователя
    public function hasPendingRequestFrom($userId)
    {
        return $this->requests()
            ->where('player_id', $userId)
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Получить заявки на вступление в команду
     */
    public function joinRequests()
    {
        return $this->hasMany(JoinRequest::class);
    }

    /**
     * Получить матчи команды
     */
    public function matches()
    {
        return $this->hasMany(GameMatch::class, 'club_id', 'id');
    }
}




