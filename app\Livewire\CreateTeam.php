<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Game;

// Импортируем наши новые интерфейсы сервисов
use App\Services\Interfaces\TeamManagerInterface;
// use App\Services\Interfaces\InvitationManagerInterface; // Больше не нужен здесь
// use App\Services\Interfaces\RequestManagerInterface; // Больше не нужен здесь

#[Title('Создание команды')]
class CreateTeam extends Component
{
    public ?int $selectedGameId = null;
    public string $gameName = '';

    // Сервисы инжектируются для использования в этом компоненте, если они нужны для общих действий.
    protected TeamManagerInterface $teamService;
    // InvitationService и RequestService теперь в дочерних компонентах

    public function boot(TeamManagerInterface $teamService)
    {
        $this->teamService = $teamService;
    }

    /**
     * Mount lifecycle hook.
     * Здесь загружаем только базовые данные, нужные для всей страницы.
     */
    public function mount(?int $defaultGameId = 1)
    {
        // Пытаемся получить gameId из сессии или устанавливаем по умолчанию
        $this->selectedGameId = session('selectedGameId', $defaultGameId);

        // Если пользователь авторизован и selectedGameId не установлен, пытаемся найти любую его команду
        if (Auth::check() && $this->selectedGameId === null) {
            $userTeams = $this->teamService->getUserTeamsForGame(Auth::id()); // Получаем все команды пользователя
            if ($userTeams->isNotEmpty()) {
                $this->selectedGameId = $userTeams->first()->game_id;
            }
        }

        // Загружаем имя игры
        $this->loadGameName();

        // Сохраняем в сессии для будущего использования (например, при перезагрузке страницы)
        session(['selectedGameId' => $this->selectedGameId]);
    }

    /**
     * Обновление выбранной игры через событие из Carousel (или другого источника)
     */
    #[On('gameSelected')]
    public function updateSelectedGame(int $gameId)
    {
        $this->selectedGameId = $gameId;
        $this->loadGameName();
        session(['selectedGameId' => $this->selectedGameId]); // Сохраняем в сессии

        // Диспатчим событие, чтобы дочерние компоненты обновили свои данные
        $this->dispatch('refreshTeamList', $gameId);
        $this->dispatch('refreshReceivedInvitations'); // ReceivedInvitationsList не зависит от gameId, но можно обновить
    }

    /**
     * Обработчик события обновления команд (когда создается/отменяется приглашение)
     */
    #[On('refreshTeamList')]
    public function handleTeamListRefresh($gameId = null)
    {
        Log::info('CreateTeam: Получено событие refreshTeamList', [
            'received_game_id' => $gameId,
            'current_selected_game_id' => $this->selectedGameId
        ]);

        // Если gameId не передан, используем текущий
        if ($gameId === null) {
            $gameId = $this->selectedGameId;
        }

        // НЕ ОТПРАВЛЯЕМ СОБЫТИЕ СНОВА - это создает цикл!
        // $this->dispatch('refreshTeamList', $gameId);

        Log::info('CreateTeam: НЕ отправляем событие refreshTeamList для предотвращения цикла');
    }

    /**
     * Обработчик события обновления приглашений
     */
    #[On('refreshReceivedInvitations')]
    public function handleInvitationsRefresh()
    {
        $this->dispatch('refreshReceivedInvitations');
    }

    /**
     * Обработчик события создания команды
     */
    #[On('teamCreated')]
    public function handleTeamCreated($gameId)
    {
        // Обновляем выбранную игру, если команда создана для другой игры
        if ($gameId !== $this->selectedGameId) {
            $this->selectedGameId = $gameId;
            $this->loadGameName();
            session(['selectedGameId' => $this->selectedGameId]);
        }

        // Обновляем компоненты
        $this->dispatch('refreshTeamList', $gameId);
        $this->dispatch('refreshReceivedInvitations');
    }

    /**
     * Получить имя игры по ID
     */
    protected function loadGameName(): void
    {
        if ($this->selectedGameId) {
            $game = Game::find($this->selectedGameId);
            $this->gameName = $game->name ?? 'Неизвестная игра';
        } else {
            $this->gameName = 'Выберите игру';
        }
    }

    /**
     * Обработчик события для перехода на страницу поиска команды
     */
    #[On('findTeamInGame')]
    public function findTeamInGame(int $gameId)
    {
        if (!$gameId) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Сначала выберите игру.']);
            return;
        }

        // Проверяем, есть ли уже команда у пользователя для этой игры через сервис
        if ($this->teamService->hasUserTeamForGame(Auth::id(), $gameId)) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Вы уже состоите в команде для этой игры.']);
            return;
        }

        session(['selectedGameId' => $gameId]);
        return $this->redirect(route('find.team', ['gameId' => $gameId]), navigate: true);
    }


    public function render()
    {
        return view('livewire.create-team', [
            'hasTeamInGame' => $this->selectedGameId ?
                                $this->teamService->hasUserTeamForGame(Auth::id(), $this->selectedGameId) :
                                false,
        ]);
    }
}