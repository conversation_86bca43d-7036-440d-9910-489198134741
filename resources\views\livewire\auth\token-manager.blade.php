<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <h2 class="mb-4">Управление токенами доступа</h2>
            
            {{-- Используем единый компонент уведомлений --}}
            @include('livewire.notification')

            <div class="row g-4">
                <!-- Левая колонка: Управление текущим токеном -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Мой токен</h5>
                        </div>
                        <div class="card-body">
                            <!-- Создание токена -->
                            <div class="mb-4">
                                <h6 class="card-subtitle mb-3">Создать новый токен</h6>
                                <div class="mb-3">
                                    <label class="form-label">Срок действия (часы)</label>
                                    <input type="number" wire:model="expiryHours" 
                                           class="form-control" min="1" max="168"
                                           placeholder="24">
                                    @error('expiryHours') 
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                                <button wire:click="generateToken" class="btn btn-primary">
                                    Создать токен
                                </button>
                            </div>

                            <!-- Информация о текущем токене -->
                            @if($currentTokenInfo)
                                <div class="border-top pt-4">
                                    <h6 class="card-subtitle mb-3">Информация о текущем токене</h6>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Статус:</span>
                                            <span class="badge {{ $currentTokenInfo['is_expired'] ? 'bg-danger' : 'bg-success' }}">
                                                {{ $currentTokenInfo['is_expired'] ? 'Истёк' : 'Активен' }}
                                            </span>
                                        </div>
                                    </div>
                                    @if(isset($currentTokenInfo['club_id']))
                                        <div class="mt-1">
                                            <small class="text-muted d-block">
                                                Club ID: {{ $currentTokenInfo['club_id'] }}
                                            </small>
                                        </div>
                                    @endif
                                    @if(isset($currentTokenInfo['expires_at']))
                                        <div class="mt-1">
                                            <small class="text-muted d-block">
                                                Истекает: {{ \Carbon\Carbon::parse($currentTokenInfo['expires_at'])->format('d.m.Y H:i') }}
                                                ({{ \Carbon\Carbon::parse($currentTokenInfo['expires_at'])->locale('ru')->diffForHumans() }})
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            @endif

                            <!-- Действия с текущим токеном -->
                            <div class="border-top pt-4">
                                <div class="d-flex gap-2">
                                    <button wire:click="getCurrentTokenInfo" class="btn btn-outline-primary">
                                        Обновить информацию
                                    </button>
                                    <button wire:click="extendToken" class="btn btn-warning">
                                        Продлить
                                    </button>
                                    <button wire:click="revokeToken" class="btn btn-danger">
                                        Отозвать
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Правая колонка: Проверка токена -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">Проверка токена</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">ID Клуба</label>
                                <input type="number" wire:model="clubId" 
                                       class="form-control" min="1"
                                       placeholder="Введите ID клуба">
                                @error('clubId') 
                                    <div class="text-danger small">{{ $message }}</div>
                                @enderror
                                @if(auth()->check())
                                    <small class="text-muted d-block">
                                        Вы можете проверять токены только для своего клуба.
                                    </small>
                                @endif
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Токен для проверки</label>
                                <input type="text" wire:model="searchToken"
                                       class="form-control"
                                       placeholder="Введите токен">
                                @error('searchToken')
                                    <div class="text-danger small">{{ $message }}</div>
                                @enderror
                            </div>

                            <button wire:click="validateToken" class="btn btn-secondary">
                                Проверить
                            </button>

                            <!-- Результат проверки -->
                            @if($showSearchTokenInfo && $searchTokenInfo)
                                <div class="mt-4">
                                    <h6 class="card-subtitle mb-3">Результат проверки</h6>
                                    <div class="alert {{ ($searchTokenInfo['is_valid'] ?? false) ? 'alert-success' : 'alert-danger' }}">
                                        <strong>Статус:</strong> 
                                        {{ $searchTokenInfo['message'] ?? ($searchTokenInfo['is_valid'] ? 'Действителен' : 'Недействителен') }}
                                    </div>
                                    
                                    @if(isset($searchTokenInfo['created_at']))
                                        <div class="mt-2">
                                            <small class="text-muted d-block">
                                                Создан: {{ \Carbon\Carbon::parse($searchTokenInfo['created_at'])->format('d.m.Y H:i') }}
                                            </small>
                                        </div>
                                    @endif
                                    
                                    @if(isset($searchTokenInfo['expires_at']))
                                        <div class="mt-1">
                                            <small class="text-muted d-block">
                                                Истекает: {{ \Carbon\Carbon::parse($searchTokenInfo['expires_at'])->format('d.m.Y H:i') }}
                                                ({{ \Carbon\Carbon::parse($searchTokenInfo['expires_at'])->locale('ru')->diffForHumans() }})
                                            </small>
                                        </div>
                                    @endif
                                    
                                    @if(isset($searchTokenInfo['last_used_at']))
                                        <div class="mt-1">
                                            <small class="text-muted d-block">
                                                Последнее использование: {{ \Carbon\Carbon::parse($searchTokenInfo['last_used_at'])->format('d.m.Y H:i') }}
                                                ({{ \Carbon\Carbon::parse($searchTokenInfo['last_used_at'])->locale('ru')->diffForHumans() }})
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('livewire:load', function () {
        @if(auth()->check())
            @this.set('clubId', {{ auth()->user()->club_id ?? 'null' }});
        @endif
    });
</script>