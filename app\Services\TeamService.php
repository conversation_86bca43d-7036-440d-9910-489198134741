<?php

namespace App\Services;

use App\Services\Interfaces\TeamManagerInterface;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use App\Models\Game;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth; // Возможно, понадобится для некоторых методов, но лучше передавать userId

class TeamService implements TeamManagerInterface
{
    /**
     * Получает список команд пользователя для указанной игры.
     * Если $gameId не указан, возвращает команды пользователя для всех игр.
     *
     * @param int $userId ID пользователя
     * @param int|null $gameId ID игры (может быть null)
     * @return \Illuminate\Support\Collection
     */
    public function getUserTeamsForGame(int $userId, ?int $gameId = null): Collection
    {
        // Ключ кэша должен учитывать, загружаем ли мы команды для конкретной игры или для всех
        $cacheKey = "user_teams_{$userId}" . ($gameId !== null ? "_{$gameId}" : "_all_games");

        // Кэшируем данные команд на 5 минут
        return Cache::remember($cacheKey, 300, function() use ($userId, $gameId) {
            $query = Team::where(function($q) use ($userId) {
                $q->whereHas('members', function($subQ) use ($userId) {
                    $subQ->where('player_id', $userId);
                })
                ->orWhere('captain_id', $userId);
            });

            // Условно добавляем фильтр по game_id, если он был передан
            if ($gameId !== null) {
                $query->where('game_id', $gameId);
            }

            return $query->with([
                'captain:id,client_nick,avatar',
                'game:id,name,team_size',
                'members.user:id,client_nick,avatar',
                'invitations' => function($query) {
                    $query->where('status', 'pending')
                        ->with('user:id,client_nick,avatar');
                },
                'requests' => function($query) {
                    $query->where('status', 'pending')
                        ->with('user:id,client_nick,avatar');
                }
            ])
            ->withCount([
                'matches',
                'matches as wins_count' => function($query) {
                    $query->where('victory', true);
                },
                'matches as losses_count' => function($query) {
                    $query->where('victory', false);
                }
            ])
            ->get();
        });
    }

    /**
     * Расформировывает команду.
     *
     * @param int $teamId ID команды
     * @param int $userId ID пользователя (капитана)
     * @return bool True в случае успеха, false в случае ошибки
     */
    public function disbandTeam(int $teamId, int $userId): bool
    {
        try {
            DB::beginTransaction();

            $team = Team::find($teamId);

            if (!$team) {
                throw new \Exception('Команда не найдена');
            }

            if ($team->captain_id !== $userId) {
                throw new \Exception('Только капитан может расформировать команду');
            }

            $gameId = $team->game_id;

            \App\Models\JoinRequest::where('team_id', $teamId)->delete();
            \App\Models\TeamInvitation::where('team_id', $teamId)->delete();
            TeamMember::where('team_id', $teamId)->delete();
            $team->delete();

            DB::commit();

            // Очищаем кэш
            Cache::forget("user_teams_{$userId}_{$gameId}");
            Cache::forget("team_stats_{$teamId}"); // Если есть отдельный кэш статистики

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при расформировании команды: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Исключает участника из команды.
     *
     * @param int $teamId ID команды
     * @param int $memberId ID исключаемого участника
     * @param int $captainId ID капитана, который выполняет действие
     * @return bool True в случае успеха, false в случае ошибки
     */
    public function kickMember(int $teamId, int $memberId, int $captainId): bool
    {
        try {
            DB::beginTransaction();

            if (!$this->isUserCaptainOfTeam($captainId, $teamId)) {
                throw new \Exception('Только капитан может исключать участников');
            }

            if ($memberId === $captainId) {
                throw new \Exception('Вы не можете исключить себя из команды');
            }

            $teamMember = TeamMember::where('team_id', $teamId)
                ->where('player_id', $memberId)
                ->first();

            if (!$teamMember) {
                throw new \Exception('Участник не найден в этой команде');
            }

            $teamMember->delete();

            // Также можно удалить принятые приглашения, если они есть (хотя после добавления в команду, их статус должен быть accepted)
            \App\Models\TeamInvitation::where('team_id', $teamId)
                ->where('player_id', $memberId)
                ->where('status', 'accepted') // Убедимся, что удаляем только принятые (старые)
                ->delete();

            $team = Team::find($teamId);
            if ($team) {
                Cache::forget("user_teams_{$captainId}_{$team->game_id}");
                Cache::forget("team_stats_{$teamId}");
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при исключении участника: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверяет, является ли пользователь капитаном команды.
     *
     * @param int $userId ID пользователя
     * @param int $teamId ID команды
     * @return bool
     */
    public function isUserCaptainOfTeam(int $userId, int $teamId): bool
    {
        return Team::where('id', $teamId)->where('captain_id', $userId)->exists();
    }

    /**
     * Проверяет, состоит ли пользователь в команде для указанной игры.
     *
     * @param int $userId ID пользователя
     * @param int $gameId ID игры
     * @return bool
     */
    public function hasUserTeamForGame(int $userId, int $gameId): bool
    {
        return Team::where('game_id', $gameId)
            ->where(function ($query) use ($userId) {
                $query->where('captain_id', $userId)
                      ->orWhereHas('members', function ($q) use ($userId) {
                          $q->where('player_id', $userId);
                      });
            })
            ->exists();
    }

    /**
     * Получает требуемый размер команды для игры.
     *
     * @param int $gameId ID игры
     * @return int
     */
    public function getRequiredTeamSize(int $gameId): int
    {
        // Здесь мы можем получить информацию из базы данных, если у Game есть поле team_size
        $game = Game::find($gameId);
        if ($game && $game->team_size) {
            return $game->team_size;
        }

        // Fallback для игр, у которых нет явного размера команды (если нужно)
        return match ((int)$gameId) {
            3 => 4, // PUBG
            default => 5, // CS2 и Dota2
        };
    }
}