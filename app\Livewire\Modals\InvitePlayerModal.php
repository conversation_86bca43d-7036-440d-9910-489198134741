<?php

namespace App\Livewire\Modals;

use Livewire\Component;
use App\Models\User;
use App\Models\TeamInvitation;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class InvitePlayerModal extends Component
{
    public $teamIdForInvite = null;
    public $playerName = '';
    public $playerOptions = [];

    protected function loadPlayerOptions()
    {
        $cacheKey = 'player_options_' . Auth::id();
        
        $this->playerOptions = Cache::remember($cacheKey, 300, function() {
            return User::where('id', '!=', Auth::id())
                ->whereNotNull('client_nick')
                ->where('client_nick', '!=', '')
                ->select('id', 'client_nick', 'avatar')
                ->orderBy('client_nick')
                ->get()
                ->map(function($player) {
                    return [
                        'id' => $player->id,
                        'name' => $player->client_nick ?: 'Пользователь #' . $player->id,
                        'avatar' => $player->avatar
                    ];
                })->toArray();
        });
    }

    public function mount($teamIdForInvite = null)
    {
        $this->teamIdForInvite = $teamIdForInvite;
        $this->loadPlayerOptions();
    }

    #[On('open-invite-player-modal')]
    public function openModal($data)
    {
        $this->teamIdForInvite = $data['teamId'];
        $this->loadPlayerOptions();
    }

    public function sendInvitation()
    {
        Log::info('InvitePlayerModal: Начало отправки приглашения', [
            'player_name' => $this->playerName,
            'team_id' => $this->teamIdForInvite
        ]);

        $this->validate([
            'playerName' => 'required',
        ], [
            'playerName.required' => 'Выберите игрока'
        ]);

        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($this->teamIdForInvite)) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Только капитан может приглашать игроков']);
            return;
        }
        
        // Находим пользователя по ID
        $invitedUser = User::find($this->playerName);
        
        if (!$invitedUser) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Пользователь не найден']);
            return;
        }
        
        // Проверяем, не отправлено ли уже приглашение
        $existingInvitation = TeamInvitation::where('team_id', $this->teamIdForInvite)
            ->where('player_id', $invitedUser->id)
            ->where('status', 'pending')
            ->first();
        
        if ($existingInvitation) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Приглашение уже отправлено этому игроку']);
            return;
        }
        
        try {
            DB::beginTransaction();
            
            // Создаем приглашение
            TeamInvitation::create([
                'team_id' => $this->teamIdForInvite,
                'player_id' => $invitedUser->id,
                'invited_by' => $user->id,
                'status' => 'pending'
            ]);
            
            DB::commit();

            // Очищаем кэш команд для капитана
            $team = \App\Models\Team::find($this->teamIdForInvite);
            if ($team) {
                // Очищаем все возможные варианты кэша
                Cache::forget("user_teams_{$user->id}_{$team->game_id}");
                Cache::forget("user_teams_{$user->id}_all_games");
                Cache::forget("user_teams_{$user->id}");

                // Очищаем кэш для приглашенного игрока
                Cache::forget("user_teams_{$invitedUser->id}_{$team->game_id}");
                Cache::forget("user_teams_{$invitedUser->id}_all_games");
                Cache::forget("user_teams_{$invitedUser->id}");

                // Очищаем кэш опций игроков
                Cache::forget('player_options_' . $user->id);

                Log::info('InvitePlayerModal: Очищен кэш', [
                    'captain_id' => $user->id,
                    'invited_user_id' => $invitedUser->id,
                    'team_id' => $team->id,
                    'game_id' => $team->game_id
                ]);
            }

            // Очищаем форму
            $this->reset(['playerName']);

            // Закрываем модальное окно через Alpine.js
            $this->dispatch('closeInviteModal');
            // Показываем уведомление об успехе
            $this->dispatch('showNotification', ['type' => 'success', 'message' => 'Приглашение успешно отправлено']);

            // Отправляем события обновления
            if ($team) {
                Log::info('InvitePlayerModal: Отправляем события обновления', [
                    'team_id' => $team->id,
                    'game_id' => $team->game_id,
                    'invited_user_id' => $invitedUser->id
                ]);

                // Отправляем специальное событие для обновления после приглашения
                $this->dispatch('invitationSent', $team->game_id);
                $this->dispatch('refreshReceivedInvitations');
            }
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при создании приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Произошла ошибка при отправке приглашения']);
        }
    }

    /**
     * Обработчик события изменения выбранного значения в селекте
     */
    #[On('select-changed')]
    public function handleSelectChanged($data)
    {
        if ($data['name'] === 'playerName') {
            $this->playerName = $data['value'];
        }
    }

    public function render()
    {
        return view('livewire.modals.invite-player-modal');
    }
}