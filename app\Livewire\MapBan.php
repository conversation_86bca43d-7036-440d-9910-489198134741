<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Team;
use App\Models\LiveMatch;
use App\Events\MapBanned;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;

class MapBan extends Component
{
    public LiveMatch $match;
    public Team $team;
    public $teamId;
    public $opponents;
    public $bannedMaps = [];
    public $availableMaps = [];
    public $currentVotingTeam = null;
    public $maxBans = 6;

    public function mount(LiveMatch $match)
    {
        $this->match = $match;
        
        // Получаем команду текущего пользователя через TeamMember
        $teamMember = \App\Models\TeamMember::where('player_id', Auth::id())->first();
        if (!$teamMember) {
            session()->flash('error', 'Вы не состоите ни в одной команде');
            return;
        }
        
        $this->team = $teamMember->team;
        $this->teamId = $this->team->id;
        
        // Определяем команду соперника
        $opponentTeamId = ($this->match->team1_id == $this->teamId) 
            ? $this->match->team2_id 
            : $this->match->team1_id;
            
        $this->opponents = Team::find($opponentTeamId);
        
        $this->loadAvailableMaps();
        $this->syncBannedMaps();
        $this->refreshVotingTeam();
    }    

    public function banMap($mapId)
    {
        if (!$this->match) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        if ($this->match->current_voting_team != $this->teamId) {
            session()->flash('error', 'Сейчас не ваша очередь банить карту');
            return;
        }

        // Проверяем, что карта еще не забанена
        if (in_array($mapId, $this->bannedMaps)) {
            session()->flash('error', 'Эта карта уже забанена');
            return;
        }

        // Используем метод модели для бана карты
        $success = $this->match->banMap($mapId, $this->teamId);

        if (!$success) {
            session()->flash('error', 'Не удалось забанить карту');
            return;
        }

        // Определяем следующую голосующую команду
        $opponentTeamId = ($this->match->team1_id == $this->teamId)
            ? $this->match->team2_id
            : $this->match->team1_id;

        // Отправляем событие
        $opponentCaptain = \App\Models\TeamMember::where('team_id', $opponentTeamId)
            ->where('role', 'captain')
            ->first();

        if ($opponentCaptain) {
            event(new MapBanned(
                matchId: $this->match->id,
                mapId: $mapId,
                fromCaptainId: Auth::id(),
                toCaptainId: $opponentCaptain->player_id,
                nextVotingTeamId: $opponentTeamId
            ));
        }

        // Обновляем локальные данные
        $this->syncBannedMaps();
        $this->refreshVotingTeam();
        $this->loadAvailableMaps();

        // Проверяем, нужно ли завершить баны
        $this->checkIfBanningComplete();
        $this->match->refresh(); // <--- ДОБАВЛЕНО: всегда обновляем матч после возможного выбора финальной карты
    }

    #[On('map.banned')]
    public function handleMapBanned($event)
    {
        Log::info('Получено событие о бане карты в MapBan', ['event' => $event]);

        if ($this->match && $this->match->id === (int)$event['matchId']) {
            // Обновляем данные матча из базы
            $this->match->refresh();

            // Синхронизируем забаненные карты
            $this->syncBannedMaps();

            // Обновляем текущую голосующую команду
            $this->refreshVotingTeam();

            // Обновляем доступные карты
            $this->loadAvailableMaps();

            // Проверяем, завершены ли баны
            $this->checkIfBanningComplete();
            $this->match->refresh(); // <--- ДОБАВЛЕНО: всегда обновляем матч после возможного выбора финальной карты

            // Воспроизводим звук уведомления
            $this->dispatch('playNotificationSound');

            // Показываем уведомление
            if ($this->currentVotingTeam && $this->currentVotingTeam->id === $this->teamId) {
                session()->flash('info', 'Оппонент забанил карту. Теперь ваша очередь!');
            } else {
                session()->flash('info', 'Оппонент забанил карту. Ожидайте своей очереди.');
            }

            Log::info('Обработано событие бана карты в MapBan', [
                'match_id' => $this->match->id,
                'current_voting_team' => $this->currentVotingTeam?->id,
                'team_id' => $this->teamId,
                'banned_maps_count' => count($this->bannedMaps)
            ]);
        }
    }

    protected function refreshVotingTeam()
    {
        if ($this->match) {
            $this->match->refresh();
            $this->currentVotingTeam = Team::find($this->match->current_voting_team);
        }
    }

    /**
     * Обновляет данные матча (вызывается автоматически каждые 3 секунды)
     */
    public function refreshMatchData()
    {
        if ($this->match) {
            $this->match->refresh();
            $this->syncBannedMaps();
            $this->refreshVotingTeam();
            $this->loadAvailableMaps();
            $this->checkIfBanningComplete();
        }
    }

    protected function syncBannedMaps()
    {
        if ($this->match) {
            try {
                $this->bannedMaps = $this->match->bannedMaps()->pluck('map_id')->toArray();
            } catch (\Exception $e) {
                Log::error('Ошибка при синхронизации забаненных карт: ' . $e->getMessage());
                $this->bannedMaps = [];
            }
        }
    }

    protected function loadAvailableMaps()
    {
        $this->availableMaps = \App\Models\Cs2Map::where('is_active', true)
            ->get(); // Теперь получаем коллекцию моделей, а не массив
    }

    /**
     * Разбанить карту (для тестирования)
     */
    public function unbanMap($mapId)
    {
        if (!$this->match) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        // Удаляем бан из базы данных
        $this->match->bannedMaps()->where('map_id', $mapId)->delete();

        // Обновляем локальные данные
        $this->syncBannedMaps();
        $this->loadAvailableMaps();

        session()->flash('success', 'Бан карты снят');
    }

    /**
     * Проверяет, завершены ли баны карт
     */
    protected function checkIfBanningComplete()
    {
        $totalMaps = \App\Models\Cs2Map::where('is_active', true)->count();
        $bannedCount = $this->match->bannedMaps()->count(); // Используем реальный подсчёт из базы
        $remainingMaps = $totalMaps - $bannedCount;

        Log::info('Проверка завершения банов', [
            'match_id' => $this->match->id,
            'total_maps' => $totalMaps,
            'banned_count' => $bannedCount,
            'remaining_maps' => $remainingMaps
        ]);

        // Если осталась только одна незабаненная карта
        if ($remainingMaps === 1) {
            // Получаем последнюю оставшуюся карту
            $finalMap = \App\Models\Cs2Map::where('is_active', true)
                ->whereNotIn('id', $this->match->bannedMaps()->pluck('map_id'))
                ->first();

            if ($finalMap) {
                // Обновляем статус матча и сохраняем выбранную карту
                $this->match->update([
                    'status' => 'live',
                    'selected_map_id' => $finalMap->id
                ]);

                // Изменяем текст уведомления
                session()->flash('info', "Выбрана карта для матча: {$finalMap->name}. Удачной игры!");

                // Отправляем событие о завершении банов
                $this->dispatch('mapVotingCompleted', [
                    'matchId' => $this->match->id,
                    'selectedMapId' => $finalMap->id,
                    'selectedMapName' => $finalMap->name
                ]);

                // Добавляем логирование
                Log::info('Выбрана финальная карта', [
                    'match_id' => $this->match->id,
                    'map_id' => $finalMap->id,
                    'map_name' => $finalMap->name
                ]);
            }
        }
    }

    public function render()
    {
        return view('livewire.map-ban');
    }
}
