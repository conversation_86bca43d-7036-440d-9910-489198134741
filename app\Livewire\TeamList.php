<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\Team; // Импортируем модель Team
use App\Models\TeamMember; // Импортируем модель TeamMember
use App\Models\Game; // Импортируем модель Game

// Импортируем сервисы
use App\Services\Interfaces\TeamManagerInterface;
use App\Services\Interfaces\InvitationManagerInterface;
use App\Services\Interfaces\RequestManagerInterface;

class TeamList extends Component
{
    // ID выбранной игры, который будет передаваться из родительского компонента CreateTeam
    public ?int $gameId = null;

    // Свойства для хранения данных, которые будут отображаться
    public array $userTeams = [];
    public array $teamMembers = []; // Члены команды
    public array $pendingInvitations = []; // Приглашения, отправленные командой
    public array $pendingRequests = []; // Заявки, полученные командой
    public array $teamStats = []; // Статистика команды

    // Инъекция сервисов
    protected TeamManagerInterface $teamService;
    protected InvitationManagerInterface $invitationService;
    protected RequestManagerInterface $requestService;

    public function boot(
        TeamManagerInterface $teamService,
        InvitationManagerInterface $invitationService,
        RequestManagerInterface $requestService
    ) {
        // Livewire 3 автоматически внедряет зависимости в метод boot, если вы не используете __construct
        $this->teamService = $teamService;
        $this->invitationService = $invitationService;
        $this->requestService = $requestService;
    }

    /**
     * Mount lifecycle hook. Инициализируем gameId, если он передан.
     * @param int|null $gameId
     */
    public function mount(?int $gameId = null)
    {
        $this->gameId = $gameId;
        $this->loadTeamData();
    }

    /**
     * Слушатель события 'refreshTeamList' из родительского компонента (или любого другого)
     * для обновления списка команд при смене игры или других событиях.
     * @param int $gameId
     */
    #[On('refreshTeamList')]
    public function refreshTeamList(int $gameId)
    {
        // Log::info('TeamList: Получено событие refreshTeamList', [
        //     'old_game_id' => $this->gameId,
        //     'new_game_id' => $gameId,
        //     'user_id' => Auth::id()
        // ]);

        $this->gameId = $gameId;

        // Принудительно очищаем кэш перед загрузкой
        if (Auth::check()) {
            Cache::forget("user_teams_" . Auth::id() . "_{$gameId}");
            Cache::forget("user_teams_" . Auth::id() . "_all_games");
        }

        $this->loadTeamData();
    }

    #[On('teamCreated')] // Событие создания новой команды
    #[On('teamDisbanded')] // Событие расформирования команды
    #[On('memberKicked')] // Событие исключения участника
    #[On('joinRequestAccepted')] // Событие принятия заявки на вступление
    #[On('joinRequestRejected')] // Событие отклонения заявки на вступление
    #[On('accepted_invitation')] // Событие принятия приглашения игроком
    #[On('invitationSent')] // Событие отправки приглашения
    public function handleTeamUpdate($gameId = null)
    {
        Log::info('TeamList: Получено событие обновления команды', [
            'event_game_id' => $gameId,
            'current_game_id' => $this->gameId
        ]);

        // Если передан gameId и он отличается от текущего, обновляем
        if ($gameId && $gameId != $this->gameId) {
            $this->gameId = $gameId;
        }

        // При любом из этих событий, просто перезагружаем все данные команд пользователя для текущей игры
        Log::info('TeamList: Вызываем loadTeamData()');
        $this->loadTeamData();
        Log::info('TeamList: loadTeamData() завершен');
    }

    /**
     * Загружает все данные, связанные с командами пользователя для текущей игры.
     */
    protected function loadTeamData(): void
    {
        if (!Auth::check() || $this->gameId === null) {
            $this->userTeams = [];
            $this->teamMembers = [];
            $this->pendingInvitations = [];
            $this->pendingRequests = [];
            $this->teamStats = [];
            return;
        }

        // Используем TeamService для получения команд
        $teams = $this->teamService->getUserTeamsForGame(Auth::id(), $this->gameId);

        Log::info('TeamList: Загружены команды', [
            'user_id' => Auth::id(),
            'game_id' => $this->gameId,
            'teams_count' => $teams->count()
        ]);

        $this->userTeams = $teams->toArray(); // Преобразуем коллекцию в массив для Livewire
        $this->teamMembers = [];
        $this->pendingInvitations = [];
        $this->pendingRequests = [];
        $this->teamStats = [];

        foreach ($teams as $team) {
            $this->teamMembers[$team->id] = $team->members->toArray();
            $this->pendingInvitations[$team->id] = $team->invitations->toArray();
            $this->pendingRequests[$team->id] = $team->requests->toArray();

            Log::info('TeamList: Данные команды', [
                'team_id' => $team->id,
                'team_name' => $team->name,
                'members_count' => count($team->members),
                'invitations_count' => count($team->invitations),
                'requests_count' => count($team->requests)
            ]);

            $this->teamStats[$team->id] = [
                'matches' => $team->matches_count,
                'wins' => $team->wins_count,
                'losses' => $team->losses_count,
                'draws' => 0, // Допустим, ничьих пока нет
                'rating' => $team->rating ?? 0,
            ];
        }
    }

    /**
     * Расформировать команду
     */
    public function disbandTeam(int $teamId)
    {
        try {
            $this->teamService->disbandTeam($teamId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Команда успешно расформирована!'
            ]);
            $this->dispatch('teamDisbanded'); // Оповещаем родителя или другие компоненты
        } catch (\Exception $e) {
            Log::error('Ошибка при расформировании команды: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Исключить участника из команды
     */
    public function kickMember(int $teamId, int $memberId)
    {
        try {
            $this->teamService->kickMember($teamId, $memberId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Участник успешно исключен из команды!'
            ]);
            $this->dispatch('memberKicked'); // Оповещаем родителя или другие компоненты
        } catch (\Exception $e) {
            Log::error('Ошибка при исключении участника: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Пригласить игрока в команду (открывает модальное окно)
     */
    public function invitePlayer(int $teamId)
    {
        // Проверяем, является ли пользователь капитаном команды
        if (!$this->teamService->isUserCaptainOfTeam(Auth::id(), $teamId)) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Только капитан может приглашать игроков.'
            ]);
            return;
        }

        $this->dispatch('open-invite-player-modal', ['teamId' => $teamId]);
    }

    /**
     * Отменить отправленное приглашение
     */
    public function cancelInvitation(int $invitationId)
    {
        try {
            $this->invitationService->cancelInvitation($invitationId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Приглашение отменено!'
            ]);

            // Обновляем данные команд
            $this->loadTeamData();

            // Уведомляем другие компоненты об изменениях
            $this->dispatch('refreshReceivedInvitations')->to('received-invitations-list');
        } catch (\Exception $e) {
            Log::error('Ошибка при отмене приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Принять заявку на вступление в команду
     */
    public function acceptRequest(int $requestId)
    {
        try {
            $this->requestService->acceptRequest($requestId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Заявка принята! Игрок добавлен в команду.'
            ]);
            $this->dispatch('joinRequestAccepted');
            $this->dispatch('refreshTeamList', $this->gameId); // Дополнительное обновление
        } catch (\Exception $e) {
            Log::error('Ошибка при принятии заявки: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Отклонить заявку на вступление в команду
     */
    public function rejectRequest(int $requestId)
    {
        try {
            $this->requestService->rejectRequest($requestId, Auth::id());
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Заявка отклонена.'
            ]);
            $this->dispatch('joinRequestRejected');
        } catch (\Exception $e) {
            Log::error('Ошибка при отклонении заявки: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Метод для настройки прослушивания каналов
     */
    public function getListeners(): array
    {
        $listeners = [];

        // Добавляем слушателей для команд пользователя
        foreach ($this->userTeams as $team) {
            // Для Livewire 3 можно использовать более короткий синтаксис для событий
            $listeners["echo:team.{$team['id']},team.member.joined"] = 'handleTeamMemberJoined';
            $listeners["echo:team.{$team['id']},team.member.left"] = 'handleTeamMemberLeft';
            $listeners["echo:team.{$team['id']},team.invitation.sent"] = 'handleTeamInvitationSent';
        }
        return $listeners;
    }

    public function handleTeamInvitationCancelled(array $event): void
    {
        // Обновляем данные только для соответствующей команды
        $this->loadTeamData();
        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Приглашение отменено!'
        ]);
    }

    public function handleTeamInvitationSent(array $event): void
    {
        // Обновляем данные только для соответствующей команды
        $this->loadTeamData();
        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Новое приглашение отправлено!'
        ]);
    }

    public function handleTeamMemberJoined(array $event): void
    {
        // Обновляем данные только для соответствующей команды
        $this->loadTeamData();
        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Новый участник присоединился к вашей команде!'
        ]);
    }

    public function handleTeamMemberLeft(array $event): void
    {
        // Обновляем данные только для соответствующей команды
        $this->loadTeamData();
        $this->dispatch('showNotification', [
            'type' => 'info',
            'message' => 'Участник покинул вашу команду.'
        ]);
    }

    /**
     * Найти соперника для команды
     */
    public function findOpponent(int $teamId)
    {
        try {
            // Проверяем, является ли пользователь капитаном команды
            if (!$this->teamService->isUserCaptainOfTeam(Auth::id(), $teamId)) {
                throw new \Exception('Только капитан может искать соперника.');
            }

            // Получаем команду для проверки game_id
            $team = Team::find($teamId);
            if (!$team) {
                throw new \Exception('Команда не найдена.');
            }

            // Проверяем количество участников команды
            $requiredSize = $this->teamService->getRequiredTeamSize($team->game_id);
            $currentMembersCount = count($this->teamMembers[$teamId] ?? []); // Используем загруженные данные

            if ($currentMembersCount < $requiredSize) {
                throw new \Exception("Для поиска соперника необходимо минимум {$requiredSize} участников в команде.");
            }

            // Сохраняем ID команды в сессии для использования на странице поиска матча
            session(['searching_team_id' => $teamId]);

            // Перенаправляем на страницу поиска матча
            return $this->redirect(route('find.match', ['teamId' => $teamId]), navigate: true);

        } catch (\Exception $e) {
            Log::error('Ошибка при поиске соперника: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Создать новый матч для команды (вызывается из кнопки)
     */
    public function createNewMatch(int $teamId)
    {
        try {
            // Проверяем, является ли пользователь капитаном команды
            if (!$this->teamService->isUserCaptainOfTeam(Auth::id(), $teamId)) {
                throw new \Exception('Только капитан может создавать новые матчи.');
            }

            // Получаем команду для проверки game_id
            $team = Team::find($teamId);
            if (!$team) {
                throw new \Exception('Команда не найдена.');
            }

            // Проверяем количество участников команды
            $requiredSize = $this->teamService->getRequiredTeamSize($team->game_id);
            $currentMembersCount = count($this->teamMembers[$teamId] ?? []);

            if ($currentMembersCount < $requiredSize) {
                throw new \Exception("Для создания матча необходимо минимум {$requiredSize} участников в команде.");
            }

            // Отправляем событие для открытия модального окна CreateMatchModal
            $this->dispatch('open-create-match-modal', ['teamId' => $teamId]);

        } catch (\Exception $e) {
            Log::error('Ошибка при создании нового матча: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }


    public function render()
    {
        return view('livewire.team-list', [
            'hasTeamInGame' => count($this->userTeams) > 0,
            'currentUserId' => Auth::id(), // Передаем ID текущего пользователя для проверок в Blade
            'gameName' => $this->gameId ? (Game::find($this->gameId)->name ?? 'Неизвестная игра') : 'Не выбрана',
        ]);
    }
}