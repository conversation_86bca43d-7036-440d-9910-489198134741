<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cs2Map extends Model
{
    protected $table = 'cs2_maps';
    
    protected $fillable = [
        'name',
        'image_url',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public $timestamps = false;

    /**
     * Связь с банами карт
     */
    public function bans()
    {
        return $this->hasMany(LiveMatchBannedMap::class, 'map_id');
    }
}