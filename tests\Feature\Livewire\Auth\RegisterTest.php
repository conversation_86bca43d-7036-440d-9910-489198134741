<?php

namespace Tests\Feature\Livewire\Auth;

use App\Livewire\Auth\Register;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Hash;
use Livewire\Livewire;
use Tests\TestCase;

class RegisterTest extends TestCase
{
    use RefreshDatabase;

    /** #[test] */
    public function registration_page_contains_livewire_component()
    {
        $this->get('/register')
            ->assertSeeLivewire('auth.register');
    }

    /** #[test] */
    public function can_register_with_valid_data()
    {
        Livewire::test(Register::class)
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->set('password_confirmation', 'password')
            ->set('clubId', 1)
            ->call('register')
            ->assertRedirect('/dashboard');

        $this->assertTrue(User::where('email', '<EMAIL>')->exists());
        $this->assertTrue(Hash::check('password', User::first()->password));
    }

    /** #[test] */
    public function club_id_is_required()
    {
        Livewire::test(Register::class)
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->set('password_confirmation', 'password')
            ->set('clubId', '')
            ->call('register')
            ->assertHasErrors(['clubId' => 'required']);
    }

    /** #[test] */
    public function loads_club_id_from_cookie()
    {
        Cookie::queue('last_club_id', 123, 60);

        Livewire::test(Register::class)
            ->assertSet('clubId', 123);
    }

    /** #[test] */
    public function loads_last_club_id_from_database_when_no_cookie()
    {
        // Create a user with club_id
        User::create([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'club_id' => 456
        ]);

        Livewire::test(Register::class)
            ->assertSet('lastClubId', 456);
    }

    /** #[test] */
    public function can_use_last_club_id()
    {
        User::create([
            'client_nick' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'club_id' => 789
        ]);

        Livewire::test(Register::class)
            ->call('useLastClubId')
            ->assertSet('clubId', 789);
    }

    /** #[test] */
    public function sets_cookie_after_successful_registration()
    {
        Livewire::test(Register::class)
            ->set('name', 'Test User')
            ->set('email', '<EMAIL>')
            ->set('password', 'password')
            ->set('password_confirmation', 'password')
            ->set('clubId', 999)
            ->call('register');

        $this->assertTrue(Cookie::hasQueued('last_club_id'));
        $this->assertEquals(999, Cookie::queued('last_club_id')->getValue());
    }

    /** #[test] */
    public function validation_rules_are_correct()
    {
        Livewire::test(Register::class)
            ->set('name', '')
            ->set('email', 'not-an-email')
            ->set('password', 'short')
            ->set('clubId', 0)
            ->call('register')
            ->assertHasErrors([
                'name' => 'required',
                'email' => 'email',
                'password' => 'min',
                'clubId' => 'min'
            ]);
    }
}