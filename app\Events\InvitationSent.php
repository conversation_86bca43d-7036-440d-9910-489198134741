<?php

namespace App\Events;

use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;
use App\Models\TeamInvitation;

class InvitationSent implements ShouldBroadcast
{
    use SerializesModels;

    public $invitation;

    public function __construct(TeamInvitation $invitation)
    {
        $this->invitation = $invitation;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('player.' . $this->invitation->player_id);
    }

    public function broadcastAs()
    {
        return 'invitation.sent';
    }

    public function broadcastWith()
    {
        return [
            'invitation' => [
                'id' => $this->invitation->id,
                'team' => [
                    'id' => $this->invitation->team->id,
                    'name' => $this->invitation->team->name,
                ],
                'created_at' => $this->invitation->created_at,
            ]
        ];
    }
} 