<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\TokenService;
use Illuminate\Support\Facades\Log;

class AuthenticateByToken
{
    protected TokenService $tokenService;
    
    public function __construct(TokenService $tokenService)
    {
        $this->tokenService = $tokenService;
    }
    
    public function handle(Request $request, Closure $next)
    {
        $clubIdFromRequest = $request->query('club');
        $authTokenFromRequest = $request->query('auth_token');

        Log::info('AuthenticateByToken: Attempting auth.', [
            'club_id' => $clubIdFromRequest, 
            'token_length' => $authTokenFromRequest ? strlen($authTokenFromRequest) : 0,
            'token' => $authTokenFromRequest
        ]);

        if ($clubIdFromRequest && $authTokenFromRequest) {
            try {
                // Валидируем токен через сервис
                $result = $this->tokenService->validateToken($authTokenFromRequest, (int)$clubIdFromRequest);

                if ($result instanceof \App\Models\User) {
                    // Авторизуем пользователя
                    $user = $result;
                    if ($user instanceof \App\Models\User) {
                        Auth::login($user);
                        Log::info('AuthenticateByToken: User logged in successfully.', [
                            'user_id' => $user->id,
                            'club_id' => $user->club_id
                        ]);
                        Log::info('AuthenticateByToken: Redirecting to team page after successful login.');
                    }
                } else {
                    $errorMessage = 'Не удалось авторизоваться. Пожалуйста, попробуйте еще раз.';
                    switch ($result) {
                        case 'invalid_token':
                            $errorMessage = 'Неверный токен.';
                            break;
                        case 'invalid_club':
                            $errorMessage = 'Неверный клуб.';
                            break;
                        case 'token_mismatch':
                            $errorMessage = 'Токен не совпадает.';
                            break;
                        case 'token_expired':
                            $errorMessage = 'Срок действия токена истек.';
                            break;
                        case 'exception':
                            $errorMessage = 'Произошла ошибка при проверке токена.';
                            break;
                        case 'user_not_found':
                            $errorMessage = 'Пользователь не найден.';
                            break;
                    }
                    Log::warning('AuthenticateByToken: Token validation failed.', [
                        'club_id' => $clubIdFromRequest,
                        'token_length' => strlen($authTokenFromRequest),
                        'error' => $errorMessage
                    ]);
                    return redirect()->route('login')->with('error', $errorMessage);
                }
            } catch (\Exception $e) {
                Log::error('AuthenticateByToken: Exception during token validation.', [
                    'error' => $e->getMessage(),
                    'club_id' => $clubIdFromRequest
                ]);
            }
        }

        return $next($request);
    }
}



