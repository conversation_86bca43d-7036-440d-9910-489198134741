<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\UsesSqliteTestDatabase;
use App\Models\LiveMatch;
use App\Models\Team;
use App\Models\User;
use App\Models\TeamMember;
use App\Models\Cs2Map;
use App\Models\LiveMatchBannedMap;
use App\Events\MapBanned;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Livewire\Livewire;

class MapBanEventTest extends TestCase
{
    use UsesSqliteTestDatabase, WithFaker;

    protected $team1;
    protected $team2;
    protected $captain1;
    protected $captain2;
    protected $match;
    protected $maps;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpSqliteDatabase();
        $this->createTestData();
    }

    protected function tearDown(): void
    {
        $this->tearDownSqliteDatabase();
        parent::tearDown();
    }

    protected function createTestData()
    {
        // Создаем карты CS2
        $this->maps = [
            Cs2Map::create(['name' => 'de_dust2', 'image_url' => 'dust2.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_mirage', 'image_url' => 'mirage.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_inferno', 'image_url' => 'inferno.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_nuke', 'image_url' => 'nuke.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_overpass', 'image_url' => 'overpass.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_train', 'image_url' => 'train.jpg', 'is_active' => true]),
            Cs2Map::create(['name' => 'de_ancient', 'image_url' => 'ancient.jpg', 'is_active' => true]),
        ];

        // Создаем пользователей-капитанов
        $this->captain1 = User::create([
            'client_nick' => 'Captain1',
            'email' => '<EMAIL>',
            'client_password' => bcrypt('password'),
            'club_id' => '584'
        ]);

        $this->captain2 = User::create([
            'client_nick' => 'Captain2',
            'email' => '<EMAIL>',
            'client_password' => bcrypt('password'),
            'club_id' => '584'
        ]);

        // Создаем команды
        $this->team1 = Team::create([
            'name' => 'Team Alpha',
            'game_id' => 1, // CS2
            'captain_id' => $this->captain1->id,
            'rating' => 1000
        ]);

        $this->team2 = Team::create([
            'name' => 'Team Beta',
            'game_id' => 1, // CS2
            'captain_id' => $this->captain2->id,
            'rating' => 1000
        ]);

        // Добавляем капитанов в команды
        TeamMember::create([
            'team_id' => $this->team1->id,
            'player_id' => $this->captain1->id,
            'role' => 'captain'
        ]);

        TeamMember::create([
            'team_id' => $this->team2->id,
            'player_id' => $this->captain2->id,
            'role' => 'captain'
        ]);

        // Создаем матч
        $this->match = LiveMatch::create([
            'team1_id' => $this->team1->id,
            'team2_id' => $this->team2->id,
            'status' => MatchStatus::MAP_VOTING->value,
            'current_voter' => CurrentVoter::TEAM1->value,
            'current_voting_team' => $this->team1->id
        ]);
    }

    /** @test */
    public function test_map_banned_event_is_dispatched()
    {
        Event::fake();

        $this->actingAs($this->captain1);

        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id);

        Event::assertDispatched(MapBanned::class, function ($event) {
            return $event->matchId === $this->match->id &&
                   $event->mapId === $this->maps[0]->id &&
                   $event->fromCaptainId === $this->captain1->id &&
                   $event->toCaptainId === $this->captain2->id &&
                   $event->nextVotingTeamId === $this->team2->id;
        });
    }

    /** @test */
    public function test_map_banned_event_has_correct_data()
    {
        Event::fake();

        $this->actingAs($this->captain1);

        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id);

        Event::assertDispatched(MapBanned::class, 1);

        $dispatchedEvent = Event::dispatched(MapBanned::class)[0][0];
        
        $this->assertEquals($this->match->id, $dispatchedEvent->matchId);
        $this->assertEquals($this->maps[0]->id, $dispatchedEvent->mapId);
        $this->assertEquals($this->captain1->id, $dispatchedEvent->fromCaptainId);
        $this->assertEquals($this->captain2->id, $dispatchedEvent->toCaptainId);
        $this->assertEquals($this->team2->id, $dispatchedEvent->nextVotingTeamId);
    }

    /** @test */
    public function test_event_not_dispatched_when_ban_fails()
    {
        Event::fake();

        // Устанавливаем ход второй команды
        $this->match->update([
            'current_voting_team' => $this->team2->id
        ]);

        $this->actingAs($this->captain1);

        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id);

        Event::assertNotDispatched(MapBanned::class);
    }

    /** @test */
    public function test_event_not_dispatched_when_map_already_banned()
    {
        Event::fake();

        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Баним карту первый раз
        $component->call('banMap', $this->maps[0]->id);
        
        // Пытаемся забанить ту же карту снова
        $component->call('banMap', $this->maps[0]->id);

        // Проверяем, что событие было отправлено только один раз
        Event::assertDispatched(MapBanned::class, 1);
    }

    /** @test */
    public function test_event_not_dispatched_when_no_opponent_captain()
    {
        Event::fake();

        // Удаляем капитана второй команды
        TeamMember::where('team_id', $this->team2->id)->delete();

        $this->actingAs($this->captain1);

        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id);

        Event::assertNotDispatched(MapBanned::class);
    }

    /** @test */
    public function test_multiple_events_dispatched_for_multiple_bans()
    {
        Event::fake();

        // Баним карту от первой команды
        $this->actingAs($this->captain1);
        $component1 = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $component1->call('banMap', $this->maps[0]->id);

        // Баним карту от второй команды
        $this->actingAs($this->captain2);
        $component2 = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        $component2->call('banMap', $this->maps[1]->id);

        // Баним карту от первой команды снова
        $this->actingAs($this->captain1);
        $component1->call('banMap', $this->maps[2]->id);

        Event::assertDispatched(MapBanned::class, 3);
    }

    /** @test */
    public function test_event_contains_correct_next_voting_team()
    {
        Event::fake();

        $this->actingAs($this->captain1);

        Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match])
            ->call('banMap', $this->maps[0]->id);

        $dispatchedEvent = Event::dispatched(MapBanned::class)[0][0];
        
        // Проверяем, что следующая голосующая команда - вторая команда
        $this->assertEquals($this->team2->id, $dispatchedEvent->nextVotingTeamId);
    }

    /** @test */
    public function test_event_handling_updates_component_state()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Сначала забаним карту в базе данных
        $this->match->banMap($this->maps[0]->id, $this->team2->id);
        
        // Симулируем событие бана карты
        $event = [
            'matchId' => $this->match->id,
            'mapId' => $this->maps[0]->id,
            'fromCaptainId' => $this->captain2->id,
            'toCaptainId' => $this->captain1->id,
            'nextVotingTeamId' => $this->team1->id
        ];
        
        $component->call('handleMapBanned', $event);
        
        // Проверяем, что забаненная карта добавлена в список
        $this->assertContains($this->maps[0]->id, $component->get('bannedMaps'));
    }

    /** @test */
    public function test_event_handling_updates_voting_team()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Симулируем событие бана карты от второй команды
        $event = [
            'matchId' => $this->match->id,
            'mapId' => $this->maps[0]->id,
            'fromCaptainId' => $this->captain2->id,
            'toCaptainId' => $this->captain1->id,
            'nextVotingTeamId' => $this->team1->id
        ];
        
        $component->call('handleMapBanned', $event);
        
        // Проверяем, что текущая голосующая команда обновилась
        $this->assertEquals($this->team1->id, $component->get('currentVotingTeam')->id);
    }

    /** @test */
    public function test_event_handling_shows_notification()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Сначала забаним карту в базе данных
        $this->match->banMap($this->maps[0]->id, $this->team2->id);
        
        // Устанавливаем current_voting_team на команду капитана1
        $this->match->update(['current_voting_team' => $this->team1->id]);
        
        // Обновляем компонент чтобы он подхватил новое состояние
        $component->call('refreshMatchData');
        
        // Симулируем событие бана карты
        $event = [
            'matchId' => $this->match->id,
            'mapId' => $this->maps[0]->id,
            'fromCaptainId' => $this->captain2->id,
            'toCaptainId' => $this->captain1->id,
            'nextVotingTeamId' => $this->team1->id
        ];
        
        $component->call('handleMapBanned', $event);
        
        // Проверяем, что уведомление реально видно в шаблоне
        $component->assertSee('Оппонент забанил карту');
    }

    /** @test */
    public function test_event_handling_dispatches_notification_sound()
    {
        $this->actingAs($this->captain1);

        $component = Livewire::test(\App\Livewire\MapBan::class, ['match' => $this->match]);
        
        // Симулируем событие бана карты
        $event = [
            'matchId' => $this->match->id,
            'mapId' => $this->maps[0]->id,
            'fromCaptainId' => $this->captain2->id,
            'toCaptainId' => $this->captain1->id,
            'nextVotingTeamId' => $this->team1->id
        ];
        
        $component->call('handleMapBanned', $event);
        
        // Проверяем, что отправлено событие воспроизведения звука
        $component->assertDispatched('playNotificationSound');
    }
} 