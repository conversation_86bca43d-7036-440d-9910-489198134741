<?php

namespace App\Services;

use App\Services\Interfaces\RequestManagerInterface;
use App\Services\Interfaces\TeamManagerInterface; // Нам понадобится TeamService для проверки капитана
use App\Models\JoinRequest;
use App\Models\TeamMember;
use App\Models\Team; // Для проверки игры команды
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class RequestService implements RequestManagerInterface
{
    protected TeamManagerInterface $teamService;

    public function __construct(TeamManagerInterface $teamService)
    {
        $this->teamService = $teamService;
    }

    /**
     * Принимает заявку на вступление в команду.
     *
     * @param int $requestId ID заявки
     * @param int $captainId ID капитана, который принимает заявку
     * @return bool
     */
    public function acceptRequest(int $requestId, int $captainId): bool
    {
        try {
            DB::beginTransaction();

            $request = JoinRequest::find($requestId);

            if (!$request) {
                throw new \Exception('Заявка не найдена.');
            }

            if (!$this->teamService->isUserCaptainOfTeam($captainId, $request->team_id)) {
                throw new \Exception('Только капитан может принимать заявки.');
            }

            // Проверить, не состоит ли игрок уже в команде (мало ли, пока заявка висела)
            $team = Team::find($request->team_id);
            if (!$team) {
                 throw new \Exception('Команда не найдена.');
            }

            if ($this->teamService->hasUserTeamForGame($request->player_id, $team->game_id)) {
                throw new \Exception('Игрок уже состоит в команде для этой игры.');
            }

            $request->status = 'accepted';
            $request->save();

            TeamMember::create([
                'team_id' => $request->team_id,
                'player_id' => $request->player_id,
                'role' => 'member'
            ]);

            // Очищаем кэш для команд пользователя
            Cache::forget("user_teams_{$request->player_id}_{$team->game_id}");
            Cache::forget("user_teams_{$captainId}_{$team->game_id}"); // Обновить для капитана тоже

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при принятии заявки: ' . $e->getMessage());
            throw $e; // Перебрасываем исключение
        }
    }

    /**
     * Отклоняет заявку на вступление в команду.
     *
     * @param int $requestId ID заявки
     * @param int $captainId ID капитана, который отклоняет заявку
     * @return bool
     */
    public function rejectRequest(int $requestId, int $captainId): bool
    {
        $request = JoinRequest::find($requestId);

        if (!$request) {
            throw new \Exception('Заявка не найдена.');
        }

        if (!$this->teamService->isUserCaptainOfTeam($captainId, $request->team_id)) {
            throw new \Exception('Только капитан может отклонять заявки.');
        }

        $request->status = 'rejected';
        return $request->save();
    }

    /**
     * Получает список ожидающих заявок на вступление для команды.
     *
     * @param int $teamId ID команды
     * @return \Illuminate\Support\Collection
     */
    public function getPendingRequestsForTeam(int $teamId): Collection
    {
        return JoinRequest::where('team_id', $teamId)
            ->where('status', 'pending')
            ->with('user:id,client_nick,avatar')
            ->get();
    }
}