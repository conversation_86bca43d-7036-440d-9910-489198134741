<?php

namespace App\Services;

use App\Services\Interfaces\InvitationManagerInterface;
use App\Services\Interfaces\TeamManagerInterface; // Добавляем
use App\Models\TeamInvitation;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class InvitationService implements InvitationManagerInterface
{
    protected TeamManagerInterface $teamService;

    public function __construct(TeamManagerInterface $teamService)
    {
        $this->teamService = $teamService;
    }

    /**
     * Отправляет приглашение игроку в команду.
     *
     * @param int $teamId ID команды
     * @param int $playerId ID игрока, которого приглашают
     * @param int $inviterId ID пользователя, который отправляет приглашение (капитан)
     * @return TeamInvitation
     * @throws \Exception
     */
    public function sendInvitation(int $teamId, int $playerId, int $inviterId): TeamInvitation
    {
        // Проверка на капитана
        if (!$this->teamService->isUserCaptainOfTeam($inviterId, $teamId)) {
            throw new \Exception('Только капитан может приглашать игроков.');
        }

        $team = Team::find($teamId);
        if (!$team) {
            throw new \Exception('Команда не найдена.');
        }

        // Проверка, состоит ли игрок уже в этой команде
        if (TeamMember::where('team_id', $teamId)->where('player_id', $playerId)->exists() || $team->captain_id === $playerId) {
            throw new \Exception('Игрок уже состоит в этой команде.');
        }

        // Проверка, есть ли уже ожидающее приглашение для этого игрока в эту команду
        if (TeamInvitation::where('team_id', $teamId)->where('player_id', $playerId)->where('status', 'pending')->exists()) {
            throw new \Exception('Приглашение этому игроку уже отправлено и ожидает ответа.');
        }

        // Проверка, не состоит ли игрок уже в другой команде для этой игры
        if ($this->teamService->hasUserTeamForGame($playerId, $team->game_id)) {
            throw new \Exception('Игрок уже состоит в команде для этой игры.');
        }

        return TeamInvitation::create([
            'team_id' => $teamId,
            'player_id' => $playerId,
            'inviter_id' => $inviterId,
            'status' => 'pending'
        ]);
    }

    /**
     * Отменяет отправленное приглашение.
     *
     * @param int $invitationId ID приглашения
     * @param int $captainId ID капитана, который отменяет приглашение
     * @return bool
     */
    public function cancelInvitation(int $invitationId, int $captainId): bool
    {
        $invitation = TeamInvitation::find($invitationId);

        if (!$invitation) {
            throw new \Exception('Приглашение не найдено.');
        }

        if (!$this->teamService->isUserCaptainOfTeam($captainId, $invitation->team_id)) {
            throw new \Exception('Только капитан может отменять приглашения.');
        }

        $teamId = $invitation->team_id;
        $playerId = $invitation->player_id;

        $result = $invitation->delete();

        if ($result) {
            // Очищаем кэш команд для капитана
            $team = Team::find($teamId);
            if ($team) {
                Cache::forget("user_teams_{$captainId}_{$team->game_id}");
                Cache::forget("user_teams_{$captainId}_all_games");

                // Очищаем кэш для приглашенного игрока
                Cache::forget("user_teams_{$playerId}_{$team->game_id}");
                Cache::forget("user_teams_{$playerId}_all_games");
            }
        }

        return $result;
    }

    /**
     * Принимает приглашение в команду.
     *
     * @param int $invitationId ID приглашения
     * @param int $playerId ID игрока, который принимает приглашение
     * @return bool
     */
    public function acceptInvitation(int $invitationId, int $playerId): bool
    {
        try {
            DB::beginTransaction();

            $invitation = TeamInvitation::find($invitationId);

            if (!$invitation) {
                throw new \Exception('Приглашение не найдено.');
            }

            if ($invitation->player_id !== $playerId) {
                throw new \Exception('У вас нет прав на это действие.');
            }

            $team = Team::find($invitation->team_id);
            if (!$team) {
                throw new \Exception('Команда не найдена.');
            }

            // Проверяем, не состоит ли игрок уже в команде для этой игры
            if ($this->teamService->hasUserTeamForGame($playerId, $team->game_id)) {
                throw new \Exception('Вы уже состоите в команде для этой игры.');
            }

            TeamMember::create([
                'team_id' => $invitation->team_id,
                'player_id' => $invitation->player_id,
                'role' => 'member'
            ]);

            $invitation->status = 'accepted';
            $invitation->save();

            // Удаляем все остальные ожидающие приглашения для этого игрока в этой игре
            \App\Models\TeamInvitation::where('player_id', $playerId)
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->delete();

            // Отклоняем все ожидающие заявки от этого игрока в другие команды в этой игре
            \App\Models\JoinRequest::where('player_id', $playerId)
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->update(['status' => 'rejected']);

            DB::commit();

            // Очищаем кэш для команд пользователя
            Cache::forget("user_teams_{$playerId}_{$team->game_id}");

            // Оповещаем команду, что новый участник присоединился (это будет сделано через события, которые мы настроим позже)
            // event(new \App\Events\TeamMemberJoined($invitation->team_id));

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при принятии приглашения: ' . $e->getMessage());
            throw $e; // Перебрасываем исключение, чтобы компонент мог его поймать и показать сообщение
        }
    }

    /**
     * Отклоняет приглашение в команду.
     *
     * @param int $invitationId ID приглашения
     * @param int $playerId ID игрока, который отклоняет приглашение
     * @return bool
     */
    public function declineInvitation(int $invitationId, int $playerId): bool
    {
        $invitation = TeamInvitation::find($invitationId);

        if (!$invitation) {
            throw new \Exception('Приглашение не найдено.');
        }

        if ($invitation->player_id !== $playerId) {
            throw new \Exception('У вас нет прав на это действие.');
        }

        $invitation->status = 'rejected';
        return $invitation->save();
    }

    /**
     * Получает список ожидающих приглашений, отправленных командой.
     *
     * @param int $teamId ID команды
     * @return \Illuminate\Support\Collection
     */
    public function getPendingInvitationsForTeam(int $teamId): Collection
    {
        return TeamInvitation::where('team_id', $teamId)
            ->where('status', 'pending')
            ->with(['user:id,client_nick,avatar', 'inviter:id,client_nick,avatar']) // inviter здесь, чтобы показать кто пригласил
            ->get();
    }

    /**
     * Получает список приглашений, полученных игроком.
     *
     * @param int $playerId ID игрока
     * @return \Illuminate\Support\Collection
     */
    public function getReceivedInvitationsForPlayer(int $playerId): Collection
    {
        return TeamInvitation::where('player_id', $playerId)
            ->where('status', 'pending')
            ->with(['team', 'team.captain:id,client_nick,avatar']) // Загружаем команду и её капитана
            ->get();
    }
}