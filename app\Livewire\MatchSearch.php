<?php

namespace App\Livewire;

use App\Models\Team;
use App\Models\LiveMatch;
use App\Models\LiveMatchReady;
use App\Models\Cs2Map;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use App\Services\MatchmakingService;
use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Events\MatchAccepted;
use Livewire\Attributes\On; 
use App\Events\MapBanned;

class MatchSearch extends Component
{
    /** @var int|null */
    public ?int $teamId = null;

    /** @var Team|null */
    public ?Team $team = null;

    /** @var string */
    public string $searchStatus = 'waiting'; // waiting, searching, found, ready_check, map_voting, live

    public $searchTime = 0;
    public $maxSearchTime = 60;
    public $foundMatch = null;
    public $opponents = [];
    public $bannedMaps = [];
    public $availableMaps = [];
    public $currentVotingTeam = null;
    public $maxBans = 6;
    
    public $showAcceptModal = false; 
    public $matchData = null; 

    public function mount($teamId, $searchStatus = 'waiting', $foundMatch = null)
    {
        $this->teamId = $teamId;
        $this->team = \App\Models\Team::find($teamId);
        $this->searchStatus = $searchStatus;
        $this->foundMatch = $foundMatch;
        $this->opponents = [];
        $this->bannedMaps = [];
        
        if ($this->team) {
            $this->loadAvailableMaps();
        }
    }

    // Новый метод для wire:init
    public function loadComponent()
    {
        // Здесь может быть любая логика, которая должна выполняться после полной загрузки компонента,
        // но для отображения спиннера достаточно оставить его пустым.
    }

    /**
     * Загружает доступные карты для выбранной игры.
     */
    protected function loadAvailableMaps()
    {
        // Проверяем, выбрана ли команда и игра
        if (!$this->team || !$this->team->game_id) {
            $this->availableMaps = [];
            return;
        }

        // Загружаем карты в зависимости от выбранной игры
        switch ($this->team->game_id) {
            case 1: // CS2
                $maps = Cs2Map::where('is_active', true)->get();
                $this->availableMaps = $maps->map(function($map) {
                    return [
                        'id' => $map->id,
                        'name' => $map->name,
                        'imageUrl' => $map->image_url
                    ];
                })->toArray();
                break;
            case 2: // Dota 2
                $this->availableMaps = [
                    ['id' => 1, 'name' => 'Default Map', 'imageUrl' => null],
                ];
                break;
            case 3: // PUBG
                $this->availableMaps = [
                    ['id' => 1, 'name' => 'Erangel', 'imageUrl' => null],
                    ['id' => 2, 'name' => 'Miramar', 'imageUrl' => null],
                    ['id' => 3, 'name' => 'Sanhok', 'imageUrl' => null],
                    ['id' => 4, 'name' => 'Vikendi', 'imageUrl' => null],
                    ['id' => 5, 'name' => 'Karakin', 'imageUrl' => null],
                    ['id' => 6, 'name' => 'Paramo', 'imageUrl' => null],
                    ['id' => 7, 'name' => 'Haven', 'imageUrl' => null],
                ];
                break;
            default:
                $this->availableMaps = [];
        }
    }

    /**
     * Начинает поиск матча.
     */
    public function startSearch()
    {
        // Проверяем, является ли пользователь капитаном команды
        $teamMember = \App\Models\TeamMember::where('team_id', $this->teamId)
            ->where('player_id', Auth::id())
            ->where('role', 'captain')
            ->first();

        // Если пользователь не является капитаном, выводим сообщение об ошибке
        if (!$teamMember) {
            session()->flash('error', 'Только капитан команды может начать поиск матча.');
            return;
        }

        // Устанавливаем статус поиска и время поиска
        $this->searchStatus = 'searching';
        $this->searchTime = 0;
        
        // Запускаем таймер поиска
        $this->dispatch('startSearchTimer');

        // Сохраняем статус поиска в сессии
        session(['search_status' => 'searching']);

        // Начинаем поиск матча
        $this->findMatch(app(MatchmakingService::class));
    }

    public function updateSearchTime()
    {
        $this->searchTime++;
    }

    public function findMatch(MatchmakingService $matchmakingService)
    {
        try {
            Log::info('Начало поиска матча', ['team_id' => $this->teamId]);
    
            $match = $matchmakingService->findMatch($this->team);
    
            if ($match) {
                Log::info('Матч найден', ['match_id' => $match->id]);
                $this->foundMatch = $match;
                $this->searchStatus = 'ready_check';
    
                $opponentTeamId = ($match->team1_id == $this->teamId) ? $match->team2_id : $match->team1_id;
                $this->opponents = [
                    'team' => Team::find($opponentTeamId)
                ];
    
                $this->dispatch('stopSearchTimer');
            } else {
                Log::warning('Матч не найден');
                session()->flash('error', 'Не удалось найти матч. Попробуйте позже.');
                $this->searchStatus = 'waiting'; 
                $this->dispatch('stopSearchTimer');
            }
        } catch (\Exception $e) {
            Log::error('Ошибка при поиске матча: ' . $e->getMessage(), ['exception' => $e, 'trace' => $e->getTraceAsString()]);
            session()->flash('error', 'Произошла ошибка при поиске матча. Пожалуйста, попробуйте еще раз.');
            $this->searchStatus = 'error';
        }
    }

    public function cancelSearch()
    {
        $this->searchStatus = 'waiting';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];
        session()->forget('search_status');
        $this->dispatch('stopSearchTimer');
    }

    /**
     * Обрабатывает событие showMatchAcceptModal.
     *
     * @param int $matchId
     */
    #[On('showMatchAcceptModal')]
    public function handleShowMatchAcceptModal($matchId)
    {
        Log::info('Received showMatchAcceptModal event in PHP', ['matchId' => $matchId]);

        // Проверяем, передан ли ID матча
        if (!$matchId) {
            Log::error('matchId missing in showMatchAcceptModal event', ['matchId' => $matchId]);
            session()->flash('error', 'Информация о матче не найдена.');
            return;
        }

        // Ищем матч
        $this->foundMatch = LiveMatch::find($matchId);
        
        // Если матч не найден, выводим сообщение об ошибке
        if (!$this->foundMatch) {
            Log::error('Match not found after receiving showMatchAcceptModal event', ['matchId' => $matchId]);
            session()->flash('error', 'Информация о матче не найдена.');
            return;
        }

        // Устанавливаем статус поиска
        $this->searchStatus = 'ready_check';
        
        // Определяем ID команды соперника
        $opponentTeamId = ($this->foundMatch->team1_id == $this->teamId)
            ? $this->foundMatch->team2_id
            : $this->foundMatch->team1_id;
        $this->opponents = ['team' => Team::find($opponentTeamId)];
        
        // Проверяем готовность команд
        $this->checkTeamsReadyStatus();
        
        // Если команды готовы, показываем модальное окно
        if ($this->searchStatus === 'ready_check') {
            $this->showAcceptModal = true;
            $this->matchData = $matchId;
        } else {
            $this->showAcceptModal = false;
        }
    }


    public function acceptMatch()
    {
        try {
            if (!$this->foundMatch) {
                Log::error('Match not found');
                session()->flash('error', 'Матч не найден');
                return;
            }
    
            $teamCaptain = \App\Models\TeamMember::where('team_id', $this->teamId)
                ->where('player_id', Auth::id())
                ->where('role', 'captain')
                ->first();
    
            $opponentTeamId = ($this->foundMatch->team1_id == $this->teamId) 
                ? $this->foundMatch->team2_id 
                : $this->foundMatch->team1_id;
    
            $opponentCaptain = \App\Models\TeamMember::where('team_id', $opponentTeamId)
                ->where('role', 'captain')
                ->first();
    
            if (!$teamCaptain || !$opponentCaptain) {
                Log::error('Captains not found', ['team_id' => $this->teamId, 'opponent_team_id' => $opponentTeamId]);
                session()->flash('error', 'Не найдены капитаны команд');
                return;
            }
    
            DB::table('live_match_ready')->updateOrInsert(
                [
                    'match_id' => $this->foundMatch->id,
                    'player_id' => $teamCaptain->player_id,
                ],
                [
                    'is_ready' => true,
                ]
            );
    
            event(new MatchAccepted(
                $this->foundMatch->id,
                $teamCaptain->player_id,
                $opponentCaptain->player_id
            ));
    
            $this->checkTeamsReadyStatus();
            
        } catch (\Exception $e) {
            Log::error('Error in acceptMatch: ' . $e->getMessage());
            session()->flash('error', 'Ошибка при принятии матча');
        }
    }
    
    /**
     * Проверяет статус готовности команд.
     */
    protected function checkTeamsReadyStatus()
    {
        $this->foundMatch->refresh();
                
        // Если обе команды готовы
        if ($this->foundMatch->areBothTeamsReady()) {
            // Если статус матча не "голосование за карты", устанавливаем его
            if ($this->foundMatch->status !== MatchStatus::MAP_VOTING->value) {
                $this->foundMatch->status = MatchStatus::MAP_VOTING->value;
                $this->foundMatch->save();
            }
            
            // Устанавливаем статус поиска
            $this->searchStatus = 'map_voting';
            $this->currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();
            $this->loadAvailableMaps();
            $this->showAcceptModal = false; // Закрываем модальное окно, если обе команды готовы
            session()->forget('info'); // Очищаем сообщение "Ожидание готовности второй команды"
            
        } else {
            // Если команды не готовы, устанавливаем статус поиска
            $this->searchStatus = 'ready_check';
            session()->flash('info', 'Ожидание готовности второй команды.');
        }
    }
    
    public function declineMatch(): void
    {
        if ($this->foundMatch) {
            $this->foundMatch->delete();
        }

        $this->searchStatus = 'waiting';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];
        session()->forget('search_status');

        $this->showAcceptModal = false;
    }

    public function findNewMatch()
    {
        $this->searchStatus = 'searching';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];

        $this->findMatch(app(MatchmakingService::class));
    }

    /**
     * Обновляет информацию о текущей голосующей команде
     */
    protected function refreshVotingTeam()
    {
        if ($this->foundMatch) {
            $this->foundMatch->refresh(); // Обновляем данные из БД
            $this->currentVotingTeam = Team::find($this->foundMatch->current_voting_team);
            
            // Логируем изменение для отладки
            Log::info('Обновлена голосующая команда', [
                'match_id' => $this->foundMatch->id,
                'current_voting_team' => $this->currentVotingTeam?->id,
                'team_name' => $this->currentVotingTeam?->name
            ]);
            
            // Обновляем состояние компонента
            $this->syncBannedMaps();
        }
    }

    public function render()
    {
        if (!is_array($this->availableMaps)) {
            $this->availableMaps = [];
        }
        
        if (!is_array($this->bannedMaps)) {
            $this->bannedMaps = [];
        }
        
        if (!is_array($this->opponents)) {
            $this->opponents = [];
        }
        
        return view('livewire.match-search');
    }

    /**
     * Синхронизирует забаненные карты
     */
    public function syncBannedMaps()
    {
        if ($this->foundMatch) {
            $this->bannedMaps = $this->foundMatch->bannedMaps->pluck('cs2_map_id')->toArray();
        } else {
            $this->bannedMaps = [];
        }
    }
}