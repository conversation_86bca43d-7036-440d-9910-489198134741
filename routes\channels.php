<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
*/

// Канал для команды
Broadcast::channel('team.{teamId}', function ($user, $teamId) {
    // Проверяем, является ли пользователь членом команды
    return $user->teams()->where('team_id', $teamId)->exists();
});
Broadcast::channel('captain.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Канал для приглашений игрока
Broadcast::channel('player.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('match-channel', function ($user) {
    return true;
});

