<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TokenService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ClearRevokedTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tokens:clear-revoked {--force : Force clear without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all revoked tokens from cache';

    protected TokenService $tokenService;

    public function __construct(TokenService $tokenService)
    {
        parent::__construct();
        $this->tokenService = $tokenService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force') && !$this->confirm('Are you sure you want to clear all revoked tokens?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        try {
            // Очищаем кэш отозванных токенов
            $this->clearRevokedTokensFromCache();
            
            $this->info('All revoked tokens have been cleared successfully.');
            Log::info('Revoked tokens cleared via command', ['user' => 'console']);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error clearing revoked tokens: ' . $e->getMessage());
            Log::error('Failed to clear revoked tokens', ['error' => $e->getMessage()]);
            
            return 1;
        }
    }

    /**
     * Очищает отозванные токены из кэша
     */
    private function clearRevokedTokensFromCache(): void
    {
        // Получаем все ключи кэша, связанные с отозванными токенами
        $keys = Cache::get('revoked_token_keys', []);
        
        if (!empty($keys)) {
            foreach ($keys as $key) {
                Cache::forget($key);
            }
        }
        
        // Очищаем список ключей
        Cache::forget('revoked_token_keys');
        
        $this->info('Cleared ' . count($keys) . ' revoked token entries from cache.');
    }
} 