<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест Pusher Real-time</title>
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Тест Pusher Real-time уведомлений</h1>
    
    @auth
    <p><strong>Пользователь:</strong> {{ auth()->user()->client_nick }} (ID: {{ auth()->id() }})</p>
    <p><strong>Канал:</strong> player.{{ auth()->id() }}</p>
    @else
    <p style="color: red;">Вы не авторизованы. <a href="/login">Войти</a></p>
    @endauth
    
    <div id="status" class="log info">Подключение к Pusher...</div>
    
    <h2>Логи событий:</h2>
    <div id="logs"></div>
    
    <script>
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const log = document.createElement('div');
            log.className = `log ${type}`;
            log.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logs.insertBefore(log, logs.firstChild);
        }
        
        @auth
        // Инициализация Pusher
        const pusher = new Pusher('{{ env('PUSHER_APP_KEY') }}', {
            cluster: '{{ env('PUSHER_APP_CLUSTER') }}',
            forceTLS: true,
            authEndpoint: '/broadcasting/auth',
            auth: {
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            }
        });
        
        // Подключение к каналу
        const channel = pusher.subscribe('private-player.{{ auth()->id() }}');
        
        // Обработка состояния подключения
        pusher.connection.bind('connected', function() {
            document.getElementById('status').innerHTML = '✅ Подключено к Pusher';
            document.getElementById('status').className = 'log success';
            addLog('Подключение к Pusher установлено', 'success');
        });
        
        pusher.connection.bind('disconnected', function() {
            document.getElementById('status').innerHTML = '❌ Отключено от Pusher';
            document.getElementById('status').className = 'log error';
            addLog('Подключение к Pusher потеряно', 'error');
        });
        
        pusher.connection.bind('error', function(err) {
            document.getElementById('status').innerHTML = '❌ Ошибка Pusher: ' + err.message;
            document.getElementById('status').className = 'log error';
            addLog('Ошибка Pusher: ' + err.message, 'error');
        });
        
        // Обработка событий канала
        channel.bind('pusher:subscription_succeeded', function() {
            addLog('Успешно подписались на канал player.{{ auth()->id() }}', 'success');
        });
        
        channel.bind('pusher:subscription_error', function(err) {
            addLog('Ошибка подписки на канал: ' + JSON.stringify(err), 'error');
        });
        
        // Слушаем события приглашений
        channel.bind('invitation.sent', function(data) {
            addLog('🎯 Получено приглашение: ' + JSON.stringify(data), 'success');
            
            if (data.invitation && data.invitation.team) {
                addLog(`📧 Вы получили приглашение в команду "${data.invitation.team.name}"`, 'info');
            }
        });
        
        channel.bind('team.invitation.cancelled', function(data) {
            addLog('❌ Приглашение отменено: ' + JSON.stringify(data), 'error');
        });
        
        // Логируем все события для отладки
        channel.bind_global(function(eventName, data) {
            addLog(`🔔 Событие "${eventName}": ${JSON.stringify(data)}`, 'info');
        });
        
        @else
        addLog('Пользователь не авторизован', 'error');
        @endauth
    </script>
</body>
</html>
