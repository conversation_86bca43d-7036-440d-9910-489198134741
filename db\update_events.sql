-- Обновление events.game_id на основе rgtournament.games.id
UPDATE events
SET game_id = (
    CASE
        WHEN LOWER(game_id) = 'cs2' THEN (SELECT id FROM rgtournament.games WHERE name = 'cs2')
        WHEN LOWER(game_id) = 'dota2' THEN (SELECT id FROM rgtournament.games WHERE name = 'dota2')
        WHEN LOWER(game_id) = 'pubg' THEN (SELECT id FROM rgtournament.games WHERE name = 'pubg')
        ELSE NULL -- Или другое значение по умолчанию, если game_id не совпадает
    END
);

-- Изменение типа данных столбца events.game_id на INT
ALTER TABLE events
MODIFY COLUMN game_id INT(11) DEFAULT NULL;