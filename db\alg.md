1. **Поиск игры:**
    *   Капитан каждой команды инициирует поиск игры.
    *   Система подбирает две команды на основе рейтинга, пинга и других критериев (например, регион).
## 2. Подтверждение игры:
*   Система отправляет уведомление капитанам обеих команд о найденной игре.
*   Каждый капитан должен подтвердить готовность к игре в течение определенного времени (например, 30 секунд).
*   Если один из капитанов не подтверждает игру вовремя, поиск начинается заново для обеих команд.
## 3. Выбор карты (бан/пик):
*   Обе команды по очереди исключают карты из общего списка доступных карт.
*   Процесс исключения (бана) карт продолжается до тех пор, пока не останется одна карта.
*   Первой исключает команда, у которой выше рейтинг (или случайно, если рейтинги равны).
*   Каждый капитан выбирает карту для исключения в течение определенного времени (например, 15 секунд).
*   Если капитан не выбирает карту вовремя, система автоматически исключает случайную карту.
## 4. Начало игры:
*   После выбора карты начинается игра.
*   Система автоматически создает сервер с выбранной картой и отправляет данные для подключения капитанам команд.

## 5. Реализовать систему таймеров через Events и Jobs
*   Добавить автоматические действия при истечении таймеров
*   Улучшить систему уведомлений между командами
*   Добавить валидацию на все действия
*   Реализовать очередность банов согласно алгоритму
*   Добавить обработку крайних случаев (отключение игрока, проблемы с сетью)

### 1. Управление модальным окном Alpine.js:
* Убран класс `fade` из модального окна, так как `x-show` и `x-transition` Alpine.js уже обеспечивают эффект появления/исчезновения. `modal` остается для базовых стилей Bootstrap.
* В `toggleAcceptModal` теперь можно передавать `matchData`, если вы захотите отобразить что-то конкретное о матче в модальном окне.
* Проверка `if (element && element.__x)` заменена на более надежный способ общения между JavaScript и Livewire/Alpine.

### 2. Обработка события `match.accepted`:**
* Вместо прямого манипулирования Alpine.js `$data` из внешнего JS, я предложил использовать `Livewire.dispatchSelf('showMatchAcceptModal', { matchId: e.matchId });`. Это более "Livewire-way" общения: вы отправляете событие *текущему* Livewire-компоненту.
* Затем, в Blade (или отдельном Alpine.js скрипте, который прослушивает Livewire события), добавляется `Livewire.on('showMatchAcceptModal', (event) => { ... });`. Этот слушатель будет реагировать на событие, отправленное из Echo, и вызовет `toggleAcceptModal` Alpine.js. Это разделяет ответственность: Echo -> Livewire -> Alpine.js.

### 3. Безопасность `$currentVotingTeam`:**
* Добавлен более строгий контроль `(int)$currentVotingTeam->id === (int)$teamId` для обеспечения, что сравнение происходит между числами и предотвращения ошибок, если `$currentVotingTeam` окажется `null`.

### 4. Улучшение Blade-синтаксиса:**
* Использование оператора нулевого слияния `??` для более лаконичного доступа к потенциально отсутствующим свойствам, например, `{{ $opponents['team']->name ?? 'Неизвестная команда' }}`.
* Определена `$opponentTeamId` переменная для улучшения читаемости.

5.  **Логирование и отладка:**
    
    ### Бан карт
    * Определить, какая команда должна банить карту следующей.
    * Хранить информацию о том, какая команда банила карту последней.
    * Проверять, что текущая команда имеет право банить карту.
    * Передавать ход бана другой команде после каждого бана.

    Теперь, когда поле `current_voting_team` добавлено в модель [`GameMatch`](app/Models/GameMatch.php), необходимо обновить компонент [`app/Livewire/MatchSearch.php`](app/Livewire/MatchSearch.php), чтобы он учитывал это поле при бане карт.
    
    | Поле                  | Тип      | Описание                                                                 |
    | --------------------- | -------- | ------------------------------------------------------------------------ |
    | `id`                  | `integer` | Уникальный идентификатор матча.                                         |
    | `team1_id`            | `integer` | Идентификатор первой команды.                                            |
    | `team2_id`            | `integer` | Идентификатор второй команды.                                            |
    | `current_voting_team` | `integer` | Идентификатор команды, которая в данный момент выбирает карту для бана. |
    | `status`              | `string`  | Статус матча (например, `pending`, `in_progress`, `completed`).         |

Необходимо:

Получать значение поля current_voting_team из модели GameMatch.
Проверять, что текущая команда имеет право банить карту (ID команды совпадает со значением поля current_voting_team).
Обновлять значение поля current_voting_team после каждого бана, чтобы ход переходил к другой команде.

#### Проверки:
- Существование матча
- Очередь команды (current_voting_team)
- Валидация массивов

#### Действия:
- Добавление карты в список забаненных
- Удаление карты из доступных
- Смена очереди голосования на другую команду
- Обновление статуса голосующей команды

#### Процесс бана карт:
1. Команды по очереди банят карты
2. После каждого бана:
    * Карта добавляется в bannedMaps
    * Удаляется из availableMaps
    * Очередь переходит к другой команде
3. Процесс продолжается до достижения maxBans или выбора финальной карты

#### Возможные улучшения:
1. Добавить валидацию максимального количества банов
2. Добавить проверку статуса матча
3. Реализовать выбор финальной карты
4. Добавить таймер на бан карты
5. Реализовать автоматический бан при истечении времени

graph TD
    A[Команда 1 банит карту] --> B[MapBan::banMap()]
    B --> C[LiveMatch::banMap()]
    C --> D[Сохранение в БД]
    C --> E[Переключение очереди]
    B --> F[Отправка события MapBanned]
    F --> G[Echo/Pusher]
    G --> H[Команда 2 получает событие]
    H --> I[MapBan::handleMapBanned()]
    I --> J[Обновление UI]
    I --> K[Проверка завершения банов]
    K --> L{Баны завершены?}
    L -->|Да| M[selectFinalMap()]
    L -->|Нет| N[Ожидание следующего бана]
    
    style A fill:#e1f5fe
    style H fill:#fff3e0
    style M fill:#e8f5e8

## План оптимизации компонентов
1.  Создать отдельные компоненты:
    * TeamList (список команд)
    * TeamMembers (участники)
    * TeamInvitations (приглашения)
    * TeamRequests (заявки)

2.  Создать сервисы:
    * TeamService (управление командами)
    * InvitationService (приглашения)
    * RequestService (заявки)

3.  Упростить систему событий:
    * Создать единый EventService
    * Унифицировать названия событий
    * Добавить документацию по событиям

4.  Добавить интерфейсы:
    * TeamManagerInterface
    * InvitationManagerInterface
    * RequestManagerInterface


# Стандарты кодирования

## Структура компонентов
1. Свойства (сгруппированные по типу)
2. Конструктор/mount
3. Методы жизненного цикла
4. Публичные методы
5. Защищенные методы
6. Приватные методы

### Документация
* PHPDoc для всех публичных методов
* Типы для всех свойств
* Комментарии на русском языке

## Проблемы, которые были выявлены:
1.  Pusher событие не отправляется ❌
**Проблема:** Код поиска приглашения для Pusher не выполняется
**Причина:** Логи показывают, что код не доходит до части с поиском приглашения
**Решение:** Нужно проверить, почему код не выполняется
2.  Бесконечный цикл обновлений ❌
**Проблема:** ReceivedInvitationsList обновляется каждые несколько миллисекунд
**Причина:** Где-то есть источник постоянных событий refreshReceivedInvitations
**Решение:** Найти и устранить источник циклических событий
3.  Приглашения не отображаются в real-time ❌
**Проблема:** У приглашенного игрока приглашения не появляются без обновления страницы
**Причина:** Pusher события не отправляются + циклические обновления мешают

## Что было реализовано:
* ✅ Pusher настроен - конфигурация корректная
* ✅ События созданы - InvitationSent, TeamInvitationCancelled
* ✅ Каналы настроены - player.{id} канал добавлен
* ✅ JavaScript обработчики - Echo слушатели добавлены

## Следующие шаги для решения:
* Исправить отправку Pusher событий
* Остановить бесконечный цикл обновлений
* Протестировать real-time функционал
## Рекомендации:
* Временно отключить избыточное логирование
* Найти источник циклических событий
* Убедиться, что Pusher события отправляются корректно
* Протестировать с двумя браузерами
Основная архитектура готова, но нужно устранить технические проблемы с циклами и отправкой событий. 🔧