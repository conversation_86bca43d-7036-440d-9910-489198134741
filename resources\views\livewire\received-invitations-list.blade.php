<div>
    @if(count($receivedInvitations) > 0)
        <div class="card mb-4 mt-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">Приглашения в команды</h6>
            </div>
            <ul class="list-group list-group-flush">
                @foreach($receivedInvitations as $invitation)
                    <li class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ $invitation['team']['name'] }}</strong>
                                <span class="text-muted ms-2">Капитан: {{ $invitation['team']['captain']['client_nick'] }}</span>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-success me-2" wire:click="acceptInvitation({{ $invitation['id'] }})">
                                    <i class="ri-check-line me-1"></i> Принять
                                </button>
                                <button class="btn btn-sm btn-danger" wire:click="declineInvitation({{ $invitation['id'] }})">
                                    <i class="ri-close-line me-1"></i> Отклонить
                                </button>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif
</div>