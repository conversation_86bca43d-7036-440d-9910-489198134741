<div>
    @if(count($receivedInvitations) > 0)
        <div class="card mb-4 mt-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">Приглашения в команды</h6>
            </div>
            <ul class="list-group list-group-flush">
                @foreach($receivedInvitations as $invitation)
                    <li class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ $invitation['team']['name'] }}</strong>
                                <span class="text-muted ms-2">Капитан: {{ $invitation['team']['captain']['client_nick'] }}</span>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-success me-2" wire:click="acceptInvitation({{ $invitation['id'] }})">
                                    <i class="ri-check-line me-1"></i> Принять
                                </button>
                                <button class="btn btn-sm btn-danger" wire:click="declineInvitation({{ $invitation['id'] }})">
                                    <i class="ri-close-line me-1"></i> Отклонить
                                </button>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif
</div>

<script>
document.addEventListener('livewire:init', () => {
    // Слушаем событие создания приглашения
    Livewire.on('invitation-created', (data) => {
        console.log('🎯 Получено событие invitation-created:', data);

        // Принудительно обновляем компонент приглашений
        Livewire.dispatch('refreshReceivedInvitations');
    });

    // Слушаем Pusher события для real-time обновлений
    @auth
    if (window.Echo) {
        console.log('🔊 Подключаемся к каналу player.{{ auth()->id() }}');

        window.Echo.private('player.{{ auth()->id() }}')
            .listen('.invitation.sent', (e) => {
                console.log('🎯 Получено Pusher событие invitation.sent:', e);

                // Обновляем список приглашений
                Livewire.dispatch('refreshReceivedInvitations');

                // Показываем уведомление
                Livewire.dispatch('showNotification', {
                    type: 'info',
                    message: `Вы получили приглашение в команду "${e.invitation.team.name}"`
                });
            })
            .listen('.team.invitation.cancelled', (e) => {
                console.log('🎯 Получено Pusher событие invitation.cancelled:', e);

                // Обновляем список приглашений
                Livewire.dispatch('refreshReceivedInvitations');

                // Показываем уведомление
                Livewire.dispatch('showNotification', {
                    type: 'warning',
                    message: 'Одно из ваших приглашений было отменено'
                });
            });
    } else {
        console.warn('⚠️ Echo не инициализирован');
    }
    @endauth
});
</script>