﻿--
-- <PERSON>ript was generated by <PERSON>art dbForge Studio for MySQL, Version **********
-- Product home page: http://www.devart.com/dbforge/mysql/studio
-- Script date 11.07.2025 8:39:21
-- Server version: 5.7.39
--

--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

--
-- Set SQL mode
--
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

--
-- Set character set the client will use to send SQL statements to the server
--
SET NAMES 'utf8';

--
-- Set default database
--
USE rgtournament;

--
-- Drop table `cache`
--
DROP TABLE IF EXISTS cache;

--
-- Drop table `clubs`
--
DROP TABLE IF EXISTS clubs;

--
-- Drop table `events`
--
DROP TABLE IF EXISTS events;

--
-- Drop table `events_copy`
--
DROP TABLE IF EXISTS events_copy;

--
-- Drop table `events_season_rating`
--
DROP TABLE IF EXISTS events_season_rating;

--
-- Drop table `jobs`
--
DROP TABLE IF EXISTS jobs;

--
-- Drop table `live_match_banned_maps`
--
DROP TABLE IF EXISTS live_match_banned_maps;

--
-- Drop table `live_match_ready`
--
DROP TABLE IF EXISTS live_match_ready;

--
-- Drop table `matches`
--
DROP TABLE IF EXISTS matches;

--
-- Drop table `matches_dota2`
--
DROP TABLE IF EXISTS matches_dota2;

--
-- Drop table `matches_pubg`
--
DROP TABLE IF EXISTS matches_pubg;

--
-- Drop table `rating`
--
DROP TABLE IF EXISTS rating;

--
-- Drop table `script_log`
--
DROP TABLE IF EXISTS script_log;

--
-- Drop table `sessions`
--
DROP TABLE IF EXISTS sessions;

--
-- Drop table `__table_transfer_progress`
--
DROP TABLE IF EXISTS __table_transfer_progress;

--
-- Drop table `join_requests`
--
DROP TABLE IF EXISTS join_requests;

--
-- Drop table `team_invitations`
--
DROP TABLE IF EXISTS team_invitations;

--
-- Drop table `team_members`
--
DROP TABLE IF EXISTS team_members;

--
-- Drop table `players`
--
DROP TABLE IF EXISTS players;

--
-- Drop table `map_votes`
--
DROP TABLE IF EXISTS map_votes;

--
-- Drop table `cs2_maps`
--
DROP TABLE IF EXISTS cs2_maps;

--
-- Drop table `matchmaking`
--
DROP TABLE IF EXISTS matchmaking;

--
-- Drop table `games`
--
DROP TABLE IF EXISTS games;

--
-- Drop table `live_match`
--
DROP TABLE IF EXISTS live_match;

--
-- Drop table `teams`
--
DROP TABLE IF EXISTS teams;

--
-- Set default database
--
USE rgtournament;

--
-- Create table `teams`
--
CREATE TABLE teams (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(100) NOT NULL,
  game_id int(11) NOT NULL,
  captain_id int(11) NOT NULL,
  rating int(11) DEFAULT 10,
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 23,
AVG_ROW_LENGTH = 3276,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `name` on table `teams`
--
ALTER TABLE teams
ADD UNIQUE INDEX name (name, game_id);

--
-- Create table `live_match`
--
CREATE TABLE live_match (
  id int(11) NOT NULL AUTO_INCREMENT,
  team1_id int(11) NOT NULL,
  team2_id int(11) NOT NULL,
  status enum ('map_voting', 'ready_check', 'live') DEFAULT 'map_voting',
  selected_map_id bigint(20) UNSIGNED DEFAULT NULL,
  current_voting_team int(11) DEFAULT NULL,
  current_voter enum ('team1', 'team2') NOT NULL DEFAULT 'team1',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 2012,
AVG_ROW_LENGTH = 8192,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE live_match
ADD CONSTRAINT live_match_ibfk_1 FOREIGN KEY (team1_id)
REFERENCES teams (id);

--
-- Create foreign key
--
ALTER TABLE live_match
ADD CONSTRAINT live_match_ibfk_2 FOREIGN KEY (team2_id)
REFERENCES teams (id);

--
-- Create table `games`
--
CREATE TABLE games (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(255) NOT NULL,
  image varchar(255) NOT NULL DEFAULT '',
  team_size smallint(5) UNSIGNED NOT NULL,
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 4,
AVG_ROW_LENGTH = 5461,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `name` on table `games`
--
ALTER TABLE games
ADD UNIQUE INDEX name (name);

--
-- Create table `matchmaking`
--
CREATE TABLE matchmaking (
  id int(11) NOT NULL AUTO_INCREMENT,
  team_id int(11) NOT NULL,
  game_id int(11) NOT NULL,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  status enum ('searching', 'matched') DEFAULT 'searching',
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE matchmaking
ADD CONSTRAINT matchmaking_ibfk_1 FOREIGN KEY (team_id)
REFERENCES teams (id);

--
-- Create foreign key
--
ALTER TABLE matchmaking
ADD CONSTRAINT matchmaking_ibfk_2 FOREIGN KEY (game_id)
REFERENCES games (id);

--
-- Create table `cs2_maps`
--
CREATE TABLE cs2_maps (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(50) NOT NULL,
  image_url varchar(255) DEFAULT NULL,
  is_active tinyint(1) DEFAULT 1,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 8,
AVG_ROW_LENGTH = 2340,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `map_votes`
--
CREATE TABLE map_votes (
  match_id int(11) NOT NULL,
  map_id int(11) NOT NULL,
  banned_by int(11) DEFAULT NULL,
  PRIMARY KEY (match_id, map_id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE map_votes
ADD CONSTRAINT map_votes_ibfk_1 FOREIGN KEY (match_id)
REFERENCES live_match (id);

--
-- Create foreign key
--
ALTER TABLE map_votes
ADD CONSTRAINT map_votes_ibfk_2 FOREIGN KEY (map_id)
REFERENCES cs2_maps (id);

--
-- Create foreign key
--
ALTER TABLE map_votes
ADD CONSTRAINT map_votes_ibfk_3 FOREIGN KEY (banned_by)
REFERENCES teams (id);

--
-- Create table `players`
--
CREATE TABLE players (
  id int(11) NOT NULL AUTO_INCREMENT,
  club_id varchar(100) DEFAULT NULL,
  client_id varchar(100) DEFAULT NULL,
  client_nick varchar(100) DEFAULT NULL,
  client_hash varchar(100) DEFAULT NULL,
  client_password varchar(100) DEFAULT NULL,
  auth_token varchar(100) DEFAULT NULL,
  auth_token_from_date datetime DEFAULT NULL,
  client_status varchar(10) DEFAULT 'web',
  change_password tinyint(1) DEFAULT 0,
  remember_token tinyint(4) DEFAULT NULL,
  email varchar(255) DEFAULT NULL,
  created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  avatar varchar(255) DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 1681,
AVG_ROW_LENGTH = 137,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `email` on table `players`
--
ALTER TABLE players
ADD UNIQUE INDEX email (email);

--
-- Create table `team_members`
--
CREATE TABLE team_members (
  id int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  player_id int(11) NOT NULL,
  team_id int(11) NOT NULL,
  role enum ('captain', 'member') NOT NULL,
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id, player_id, team_id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 32,
AVG_ROW_LENGTH = 1820,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create foreign key
--
ALTER TABLE team_members
ADD CONSTRAINT team_members_ibfk_1 FOREIGN KEY (player_id)
REFERENCES players (id);

--
-- Create foreign key
--
ALTER TABLE team_members
ADD CONSTRAINT team_members_ibfk_2 FOREIGN KEY (team_id)
REFERENCES teams (id);

--
-- Create table `team_invitations`
--
CREATE TABLE team_invitations (
  id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  team_id int(11) NOT NULL,
  player_id int(11) NOT NULL,
  invited_by int(11) NOT NULL,
  status enum ('pending', 'accepted', 'rejected') NOT NULL DEFAULT 'pending',
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 22,
AVG_ROW_LENGTH = 1365,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `team_invitations_team_id_player_id_status_unique` on table `team_invitations`
--
ALTER TABLE team_invitations
ADD UNIQUE INDEX team_invitations_team_id_player_id_status_unique (team_id, player_id, status);

--
-- Create foreign key
--
ALTER TABLE team_invitations
ADD CONSTRAINT team_invitations_invited_by_foreign FOREIGN KEY (invited_by)
REFERENCES players (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE team_invitations
ADD CONSTRAINT team_invitations_player_id_foreign FOREIGN KEY (player_id)
REFERENCES players (id) ON DELETE CASCADE;

--
-- Create foreign key
--
ALTER TABLE team_invitations
ADD CONSTRAINT team_invitations_team_id_foreign FOREIGN KEY (team_id)
REFERENCES teams (id) ON DELETE CASCADE;

--
-- Create table `join_requests`
--
CREATE TABLE join_requests (
  id int(11) NOT NULL AUTO_INCREMENT,
  player_id int(11) NOT NULL,
  team_id int(11) NOT NULL,
  status enum ('pending', 'accepted', 'rejected') DEFAULT 'pending',
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 11,
AVG_ROW_LENGTH = 16384,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `player_id` on table `join_requests`
--
ALTER TABLE join_requests
ADD UNIQUE INDEX player_id (player_id, team_id);

--
-- Create foreign key
--
ALTER TABLE join_requests
ADD CONSTRAINT join_requests_ibfk_1 FOREIGN KEY (player_id)
REFERENCES players (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create foreign key
--
ALTER TABLE join_requests
ADD CONSTRAINT join_requests_ibfk_2 FOREIGN KEY (team_id)
REFERENCES teams (id) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Create table `__table_transfer_progress`
--
CREATE TABLE __table_transfer_progress (
  name varchar(255) NOT NULL,
  lsn bigint(20) DEFAULT NULL,
  status enum ('SnapshotWait', 'SyncWait', 'InSync') DEFAULT NULL,
  PRIMARY KEY (name)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 1820,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `sessions`
--
CREATE TABLE sessions (
  id varchar(255) NOT NULL,
  user_id bigint(20) UNSIGNED DEFAULT NULL,
  ip_address varchar(45) DEFAULT NULL,
  user_agent text DEFAULT NULL,
  payload longtext NOT NULL,
  last_activity int(11) NOT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 16384,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `sessions_last_activity_index` on table `sessions`
--
ALTER TABLE sessions
ADD INDEX sessions_last_activity_index (last_activity);

--
-- Create index `sessions_user_id_index` on table `sessions`
--
ALTER TABLE sessions
ADD INDEX sessions_user_id_index (user_id);

--
-- Create table `script_log`
--
CREATE TABLE script_log (
  id int(11) NOT NULL AUTO_INCREMENT,
  date datetime DEFAULT NULL,
  script_name varchar(50) DEFAULT NULL,
  log text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `rating`
--
CREATE TABLE rating (
  id int(11) NOT NULL AUTO_INCREMENT,
  club_id int(11) DEFAULT NULL,
  player_id int(11) DEFAULT NULL,
  season_id int(11) DEFAULT NULL,
  game_id varchar(10) DEFAULT NULL,
  game_rating int(11) DEFAULT NULL,
  game_now varchar(10) DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 4844,
AVG_ROW_LENGTH = 47,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `matches_pubg`
--
CREATE TABLE matches_pubg (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  club_id int(11) DEFAULT 0,
  player_id int(11) DEFAULT NULL,
  date datetime DEFAULT NULL,
  log text DEFAULT NULL,
  log_gameover text DEFAULT NULL,
  victory int(11) DEFAULT NULL,
  score int(11) DEFAULT NULL,
  math_score varchar(45) DEFAULT '0/0',
  date_scan datetime DEFAULT NULL,
  col_scan int(11) DEFAULT 0,
  gamemode varchar(45) DEFAULT NULL,
  mapname varchar(45) DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 1503,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_player_date` on table `matches_pubg`
--
ALTER TABLE matches_pubg
ADD INDEX idx_player_date (player_id, date);

--
-- Create index `idx_player_date_victory` on table `matches_pubg`
--
ALTER TABLE matches_pubg
ADD INDEX idx_player_date_victory (player_id, date, victory);

--
-- Create index `idx_player_id` on table `matches_pubg`
--
ALTER TABLE matches_pubg
ADD INDEX idx_player_id (player_id);

--
-- Create table `matches_dota2`
--
CREATE TABLE matches_dota2 (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  club_id int(11) DEFAULT 0,
  player_id int(11) DEFAULT NULL,
  date datetime DEFAULT NULL,
  log longtext DEFAULT NULL,
  log_gameover longtext DEFAULT NULL,
  victory int(11) DEFAULT NULL,
  score int(11) DEFAULT NULL,
  math_score varchar(45) DEFAULT NULL,
  match_id bigint(20) DEFAULT NULL,
  hero_id int(11) DEFAULT NULL,
  date_scan datetime DEFAULT NULL,
  col_scan int(11) DEFAULT 0,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 14918,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_player_date` on table `matches_dota2`
--
ALTER TABLE matches_dota2
ADD INDEX idx_player_date (player_id, date);

--
-- Create index `idx_player_date_victory` on table `matches_dota2`
--
ALTER TABLE matches_dota2
ADD INDEX idx_player_date_victory (player_id, date, victory);

--
-- Create index `idx_player_id` on table `matches_dota2`
--
ALTER TABLE matches_dota2
ADD INDEX idx_player_id (player_id);

--
-- Create table `matches`
--
CREATE TABLE matches (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  club_id int(11) DEFAULT 0,
  player_id int(11) DEFAULT NULL,
  date datetime DEFAULT NULL,
  log text DEFAULT NULL,
  log_gameover text DEFAULT NULL,
  victory int(11) DEFAULT 0,
  score int(11) DEFAULT 0,
  math_score varchar(45) DEFAULT NULL,
  current_voting_team int(11) DEFAULT NULL,
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 46203,
AVG_ROW_LENGTH = 2996,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `idx_player_date` on table `matches`
--
ALTER TABLE matches
ADD INDEX idx_player_date (player_id, date);

--
-- Create index `idx_player_id` on table `matches`
--
ALTER TABLE matches
ADD INDEX idx_player_id (player_id);

--
-- Create table `live_match_ready`
--
CREATE TABLE live_match_ready (
  match_id int(11) NOT NULL,
  player_id int(11) NOT NULL,
  is_ready tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (match_id, player_id)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 4096,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `live_match_banned_maps`
--
CREATE TABLE live_match_banned_maps (
  id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  match_id bigint(20) UNSIGNED NOT NULL,
  map_id bigint(20) UNSIGNED NOT NULL,
  team_id bigint(20) UNSIGNED NOT NULL,
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 59,
AVG_ROW_LENGTH = 292,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `live_match_banned_maps_map_id_index` on table `live_match_banned_maps`
--
ALTER TABLE live_match_banned_maps
ADD INDEX live_match_banned_maps_map_id_index (map_id);

--
-- Create index `live_match_banned_maps_match_id_index` on table `live_match_banned_maps`
--
ALTER TABLE live_match_banned_maps
ADD INDEX live_match_banned_maps_match_id_index (match_id);

--
-- Create index `live_match_banned_maps_team_id_index` on table `live_match_banned_maps`
--
ALTER TABLE live_match_banned_maps
ADD INDEX live_match_banned_maps_team_id_index (team_id);

--
-- Create table `jobs`
--
CREATE TABLE jobs (
  id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  queue varchar(255) NOT NULL,
  payload longtext NOT NULL,
  attempts tinyint(3) UNSIGNED NOT NULL,
  reserved_at int(10) UNSIGNED DEFAULT NULL,
  available_at int(10) UNSIGNED NOT NULL,
  created_at int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 551,
AVG_ROW_LENGTH = 1191,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create index `jobs_queue_index` on table `jobs`
--
ALTER TABLE jobs
ADD INDEX jobs_queue_index (queue);

--
-- Create table `events_season_rating`
--
CREATE TABLE events_season_rating (
  id int(11) NOT NULL AUTO_INCREMENT,
  event_id int(11) DEFAULT NULL,
  player_id int(11) DEFAULT NULL,
  rating int(11) DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 5636,
AVG_ROW_LENGTH = 50,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `events_copy`
--
CREATE TABLE events_copy (
  id int(11) NOT NULL AUTO_INCREMENT,
  club_id int(11) DEFAULT NULL,
  game_id varchar(10) DEFAULT NULL,
  type varchar(10) DEFAULT NULL,
  name varchar(50) DEFAULT NULL,
  date_from datetime DEFAULT NULL,
  date_to datetime DEFAULT NULL,
  token text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `events`
--
CREATE TABLE events (
  id int(11) NOT NULL AUTO_INCREMENT,
  club_id int(11) DEFAULT NULL,
  game_id int(11) DEFAULT NULL,
  type varchar(10) DEFAULT NULL,
  name varchar(50) DEFAULT NULL,
  date_from datetime DEFAULT NULL,
  date_to datetime DEFAULT NULL,
  token text DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 239,
AVG_ROW_LENGTH = 163,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `clubs`
--
CREATE TABLE clubs (
  id int(11) NOT NULL AUTO_INCREMENT,
  club_id int(11) DEFAULT NULL,
  club_name varchar(50) DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
AUTO_INCREMENT = 107,
AVG_ROW_LENGTH = 273,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_general_ci,
ROW_FORMAT = DYNAMIC;

--
-- Create table `cache`
--
CREATE TABLE cache (
  `key` varchar(255) NOT NULL,
  value mediumtext NOT NULL,
  expiration int(11) NOT NULL,
  PRIMARY KEY (`key`)
)
ENGINE = INNODB,
AVG_ROW_LENGTH = 14456,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci,
ROW_FORMAT = DYNAMIC;

--
-- Restore previous SQL mode
--
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS = @OLD_FOREIGN_KEY_CHECKS */;