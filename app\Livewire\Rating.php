<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Game;
use App\Models\Season; // Если модель Season используется напрямую для рейтинга
use App\Models\Event;
use App\Models\FullRating; // Это твоя модель для общего/клубного рейтинга
use App\Models\Player; // Используется для получения ника Auth::id()
use Carbon\Carbon;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB; // Убедимся, что DB импортирован
use Illuminate\Support\Facades\Log;

#[Title('Рейтинг игроков')]
class Rating extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';
    
    public $ratingType = 'club';
    public $selectedGameId = null;
    public $selectedSeasonId = null;
    public $games = [];
    public $seasons = [];
    public $perPage = 10;
    public $page = 1;

    public $authenticatedPlayerRank = null;
    public $authenticatedPlayerPage = null;
    public $highlightPlayerId = null;
    public $scroll = null;
    public $playerPosition = null; // Добавляем свойство для хранения места игрока

    protected $queryString = [
        'page' => ['except' => 1],
        'ratingType' => ['except' => 'club'],
        'selectedGameId' => ['except' => null],
        'selectedSeasonId' => ['except' => null],
        'perPage' => ['except' => 10],
        'highlightPlayerId' => ['except' => null, 'as' => 'highlight'],
        'scroll' => ['except' => null],
    ];

    /**
     * Загружает игры и сезоны.
     */
    protected function loadGamesAndSeasons()
    {
        // Загружаем все игры
        $this->games = Game::all();
        
        // Загружаем текущие сезоны
        $this->seasons = Event::whereDate('date_from', '<=', Carbon::now())
                      ->whereDate('date_to', '>=', Carbon::now())
                      ->get();

        // Устанавливаем текущий сезон
        $this->selectedSeasonId = $this->seasons->first()?->id;
    }

    /**
     * Инициализирует страницу пользователя.
     */
    protected function initializeUserPage()
    {
        if (Auth::check()) {
            $userId = Auth::id();
            $this->highlightPlayerId = $userId;
            
            // Получаем ранг и страницу для текущего пользователя
            $this->getAuthenticatedPlayerRankAndPage();
            
            // Если есть страница, устанавливаем её
            if ($this->authenticatedPlayerPage) {
                $this->setPage($this->authenticatedPlayerPage);
            }
        }
    }

    /**
     * Mount lifecycle hook.
     *
     * @param string|null $type
     * @param string|null $highlight
     * @param string|null $scroll
     */
    public function mount($type = null, $highlight = null, $scroll = null)
    {
        // Устанавливаем тип рейтинга в зависимости от параметра
        if ($type === 'club') {
            $this->ratingType = 'club';
        } elseif ($type === 'general') {
            $this->ratingType = 'general';
        }

        // Загружаем игры и сезоны
        $this->loadGamesAndSeasons();
        
        // Устанавливаем выбранную игру из сессии или первую доступную
        $this->selectedGameId = session('selectedGameId', $this->games->first()?->id);

        // Инициализируем страницу пользователя
        $this->initializeUserPage();
        
        // Если передан параметр highlight, устанавливаем его и получаем страницу
        if ($highlight) {
            $this->highlightPlayerId = $highlight;
            
            // Получаем ранг и страницу для подсвечиваемого игрока
            $this->getAuthenticatedPlayerRankAndPage();
            
            // Если есть страница, устанавливаем её
            if ($this->authenticatedPlayerPage) {
                $this->setPage($this->authenticatedPlayerPage);
            }
        }
        
        // Если передан параметр scroll, устанавливаем страницу
        if ($scroll) {
            $this->setPage(ceil($scroll / $this->perPage));
        }
    }

    /**
     * Обновляет выбранный ID игры.
     */
    public function updatedSelectedGameId()
    {
        session(['selectedGameId' => $this->selectedGameId]);
        $this->resetPage();
        $this->getAuthenticatedPlayerRankAndPage(); // Обновляем ранг при смене игры
        $this->dispatch('gameChanged', gameId: $this->selectedGameId);
    }

    /**
     * Обновляет тип рейтинга.
     */
    public function updatedRatingType()
    {
        $this->resetPage();
        $this->getAuthenticatedPlayerRankAndPage(); // Обновляем ранг при смене типа рейтинга
    }

    /**
     * Выбирает сезон.
     *
     * @param int $seasonId
     */
    public function selectSeason($seasonId)
    {
        $this->selectedSeasonId = $seasonId;
        $this->resetPage();
        $this->getAuthenticatedPlayerRankAndPage(); // Обновляем ранг при смене сезона
    }

    /**
     * Обновляет количество элементов на странице.
     */
    public function updatedPerPage()
    {
        session(['perPage' => $this->perPage]);
        $this->resetPage();
        $this->getAuthenticatedPlayerRankAndPage(); // Обновляем ранг при смене кол-ва на страницу
    }

    /**
     * Получает ранг и страницу авторизованного пользователя.
     */
    protected function getAuthenticatedPlayerRankAndPage()
    {
        if (!Auth::check()) {
            $this->authenticatedPlayerRank = null;
            $this->authenticatedPlayerPage = null;
            $this->playerPosition = null;
            return;
        }

        $userId = Auth::id();

        // Строим базовый запрос для определения ранга пользователя
        $baseQuery = null;
        if ($this->ratingType === 'season') {
            $baseQuery = \App\Models\Season::select('player_id', 'rating as game_rating')
                ->where('player_id', $userId)
                ->when($this->selectedSeasonId, function($q) {
                    $q->where('event_id', $this->selectedSeasonId);
                })
                ->when($this->selectedGameId, function($q) {
                    $q->whereHas('event', function($sq) {
                        $sq->where('game_id', $this->selectedGameId);
                    });
                });
        } else {
            $baseQuery = \App\Models\FullRating::select('player_id', 'game_rating', 'club_id')
                ->where('player_id', $userId)
                ->when($this->selectedGameId, function ($q) {
                    $q->where('game_id', $this->selectedGameId);
                });

            if ($this->ratingType === 'club' && Auth::check()) {
                $baseQuery->where('club_id', Auth::user()->club_id ?? null);
            }
        }

        if (!$baseQuery) {
            $this->authenticatedPlayerRank = null;
            $this->authenticatedPlayerPage = null;
            $this->playerPosition = null;
            return;
        }

        // Получаем рейтинг текущего игрока
        $playerRating = $baseQuery->value('game_rating');

        if (!$playerRating) {
            $this->authenticatedPlayerRank = null;
            $this->authenticatedPlayerPage = null;
            $this->playerPosition = null;
            return;
        }

        // Считаем количество игроков с рейтингом выше текущего
        $rankQuery = null;
        if ($this->ratingType === 'season') {
            $rankQuery = \App\Models\Season::where('rating', '>', $playerRating)
                ->when($this->selectedSeasonId, function($q) {
                    $q->where('event_id', $this->selectedSeasonId);
                })
                ->when($this->selectedGameId, function($q) {
                    $q->whereHas('event', function($sq) {
                        $sq->where('game_id', $this->selectedGameId);
                    });
                });
        } else {
            $rankQuery = \App\Models\FullRating::where('game_rating', '>', $playerRating)
                ->when($this->selectedGameId, function ($q) {
                    $q->where('game_id', $this->selectedGameId);
                });

            if ($this->ratingType === 'club' && Auth::check()) {
                $rankQuery->where('club_id', Auth::user()->club_id ?? null);
            }
        }

        // Получаем общее количество игроков
        $totalPlayers = null;
        if ($this->ratingType === 'season') {
            $totalPlayers = \App\Models\Season::when($this->selectedSeasonId, function($q) {
                    $q->where('event_id', $this->selectedSeasonId);
                })
                ->when($this->selectedGameId, function($q) {
                    $q->whereHas('event', function($sq) {
                        $sq->where('game_id', $this->selectedGameId);
                    });
                })
                ->count();
        } else {
            $totalPlayers = \App\Models\FullRating::when($this->selectedGameId, function ($q) {
                    $q->where('game_id', $this->selectedGameId);
                })
                ->when($this->ratingType === 'club' && Auth::check(), function($q) {
                    $q->where('club_id', Auth::user()->club_id ?? null);
                })
                ->count();
        }

        // Считаем количество игроков с рейтингом выше текущего
        $playersAbove = $rankQuery->count();

        // Вычисляем позицию
        $this->authenticatedPlayerRank = $playersAbove + 1;
        $this->playerPosition = $this->authenticatedPlayerRank;
        
        // Вычисляем страницу
        $this->authenticatedPlayerPage = ceil($this->authenticatedPlayerRank / $this->perPage);
    }

    /**
     * Получает ранг игрока.
     *
     * @param int $playerId
     * @param int $gameId
     * @param int|null $clubId
     * @return int|null
     */
    public function getPlayerRank($playerId, $gameId, $clubId = null)
    {
        $query = FullRating::where('game_id', $gameId);
        
        if ($clubId) {
            $query->where('club_id', $clubId);
        }

        // Получаем рейтинг текущего игрока
        $playerRating = $query->where('player_id', $playerId)
            ->value('game_rating');

        if (!$playerRating) {
            return null;
        }

        // Считаем количество игроков с рейтингом выше текущего
        $rank = $query->where('game_rating', '>', $playerRating)
            ->count();

        // Добавляем 1, так как позиция начинается с 1
        return $rank + 1;
    }

    /**
     * Переходит к позиции текущего пользователя.
     */
    public function gotoMyPosition()
    {
        if (!Auth::check()) {
            return;
        }

        $this->getAuthenticatedPlayerRankAndPage();
        
        if ($this->authenticatedPlayerPage) {
            $this->setPage($this->authenticatedPlayerPage);
            $this->highlightPlayerId = Auth::id();
        }
    }

    /**
     * Render the component.
     */
    public function render()
    {
        $ratingsQuery = null;

        if ($this->ratingType === 'season') {
            // Сезонный рейтинг
            $ratingsQuery = Season::with(['player', 'event'])
                ->orderBy('rating', 'desc'); // Используем 'rating' для сезонного рейтинга

            // Если выбран сезон, фильтруем по нему
            if ($this->selectedSeasonId) {
                $ratingsQuery->where('event_id', $this->selectedSeasonId);
            }

            // Если выбрана игра, фильтруем по ней через отношение 'event'
            if ($this->selectedGameId) {
                $ratingsQuery->whereHas('event', function($query) {
                    $query->where('game_id', $this->selectedGameId);
                });
            }

        } else {
            // Общий рейтинг ('club' или 'general')
            $ratingsQuery = FullRating::with(['player', 'game', 'club'])
                ->orderBy('game_rating', 'desc'); // Используем 'game_rating' для FullRating

            // Если выбрана игра, фильтруем по ней
            if ($this->selectedGameId) {
                $ratingsQuery->where('game_id', $this->selectedGameId);
            }

            // Если клубный рейтинг и пользователь авторизован, фильтруем по клубу
            if ($this->ratingType === 'club' && Auth::check()) {
                $ratingsQuery->where('club_id', Auth::user()->club_id ?? null);
            }
        }
        
        $ratings = $ratingsQuery->paginate($this->perPage);
        
        // Добавляем позицию для каждого рейтинга вручную после пагинации
        $ratings->getCollection()->transform(function ($rating, $key) use ($ratings) {
            $rating->position = ($ratings->currentPage() - 1) * $ratings->perPage() + $key + 1;
            return $rating;
        });

        // Добавляем параметры к URL пагинации
        $ratings->appends(request()->query());
        
        return view('livewire.rating', [
            'ratings' => $ratings,
            'highlightPlayerId' => $this->highlightPlayerId // Используем свойство Livewire
        ]);
    }
}