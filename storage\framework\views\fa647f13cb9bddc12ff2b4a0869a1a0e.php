
<div>
    <?php $__env->startSection('title', 'Создание команды'); ?>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-body">
                        <a href="<?php echo e(route('team')); ?>" class="btn btn-sm btn-outline-primary" wire:navigate="">
                            <i class="ri-arrow-left-line me-1"></i> Вернуться
                        </a>                        

                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('carousel', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1087401664-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                        <?php echo $__env->make('livewire.notification', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('team-list', ['gameId' => $selectedGameId]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1087401664-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                        
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('received-invitations-list', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1087401664-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('modals.create-team-modal', ['selectedGameId' => $selectedGameId]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1087401664-3', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('modals.invite-player-modal', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1087401664-4', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('modals.create-match-modal', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1087401664-5', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    <script>
        document.addEventListener('livewire:initialized', () => {
            // Обработчики для закрытия модальных окон (если они вызываются из разных мест)
            Livewire.on('closeInviteModal', () => {
                console.log('🔄 Событие closeInviteModal получено');
                window.dispatchEvent(new CustomEvent('closeInviteModal'));
            });

            Livewire.on('closeCreateTeamModal', () => {
                console.log('🔄 Событие closeCreateTeamModal получено');
                window.dispatchEvent(new CustomEvent('closeCreateTeamModal'));
            });

            Livewire.on('showNotification', (data) => {
                console.log('📢 Событие showNotification получено:', data);
                window.dispatchEvent(new CustomEvent('showNotification', { detail: data }));
            });

            Livewire.on('refreshTeamList', (data) => {
                console.log('🔄 Событие refreshTeamList получено в браузере:', data);
            });

            Livewire.on('refreshReceivedInvitations', (data) => {
                console.log('🔄 Событие refreshReceivedInvitations получено в браузере:', data);
            });
        });
    </script>
</div><?php /**PATH C:\OSPanel\domains\web-rgtournament2\resources\views/livewire/create-team.blade.php ENDPATH**/ ?>