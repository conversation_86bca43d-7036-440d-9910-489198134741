<!DOCTYPE html>
<html>
<head>
    <title>Test Match Accepted Event</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h3>Тест события MatchAccepted</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Информация:</strong><br>
                    Этот тест проверяет работу события MatchAccepted через Pusher.<br>
                    Текущий пользователь: {{ auth()->id() ?? 'Не авторизован' }}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>Отправка события</h5>
                        <form id="testForm">
                            <div class="mb-3">
                                <label for="matchId" class="form-label">Match ID</label>
                                <input type="number" class="form-control" id="matchId" value="1832">
                            </div>
                            <div class="mb-3">
                                <label for="fromCaptainId" class="form-label">From Captain ID</label>
                                <input type="number" class="form-control" id="fromCaptainId" value="1665">
                            </div>
                            <div class="mb-3">
                                <label for="toCaptainId" class="form-label">To Captain ID</label>
                                <input type="number" class="form-control" id="toCaptainId" value="{{ auth()->id() ?? 1671 }}">
                            </div>
                            <button type="submit" class="btn btn-primary">Отправить событие</button>
                        </form>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Статус подключения</h5>
                        <div id="status" class="badge bg-secondary">Не подключен</div>
                        
                        <h5 class="mt-3">Лог событий</h5>
                        <div id="log" class="border p-3" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Инициализация...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let pusher;
        let channel;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className = 'bg-secondary') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `badge ${className}`;
        }
        
        function initPusher() {
            try {
                log('Инициализируем Pusher...');
                
                // Включаем отладку
                Pusher.logToConsole = true;
                
                pusher = new Pusher('{{ env('VITE_PUSHER_APP_KEY') }}', {
                    cluster: '{{ env('VITE_PUSHER_APP_CLUSTER') }}',
                    forceTLS: true,
                    auth: {
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    }
                });
                
                log('Pusher создан');
                
                // Обработчики событий подключения
                pusher.connection.bind('connected', function() {
                    log('✅ Подключено к Pusher!');
                    updateStatus('Подключено', 'bg-success');
                    subscribeToChannel();
                });
                
                pusher.connection.bind('disconnected', function() {
                    log('❌ Отключено от Pusher');
                    updateStatus('Отключено', 'bg-warning');
                });
                
                pusher.connection.bind('error', function(err) {
                    log('🚨 Ошибка подключения: ' + JSON.stringify(err));
                    updateStatus('Ошибка', 'bg-danger');
                });
                
                pusher.connection.bind('state_change', function(states) {
                    log(`🔄 Смена состояния: ${states.previous} → ${states.current}`);
                    updateStatus(states.current);
                });
                
                log('Обработчики событий установлены');
                
            } catch (error) {
                log('🚨 Ошибка инициализации: ' + error.message);
                updateStatus('Ошибка инициализации', 'bg-danger');
            }
        }
        
        function subscribeToChannel() {
            if (!pusher) {
                log('Pusher не инициализирован');
                return;
            }
            
            const captainId = {{ auth()->id() ?? 1671 }};
            const channelName = `private-captain.${captainId}`;

            log(`Подписываемся на канал ${channelName}...`);
            
            channel = pusher.subscribe(channelName);
            
            channel.bind('pusher:subscription_succeeded', function() {
                log('✅ Подписка на канал успешна');
            });
            
            channel.bind('pusher:subscription_error', function(err) {
                log('🚨 Ошибка подписки: ' + JSON.stringify(err));
            });
            
            channel.bind('match.accepted', function(data) {
                log('📨 Получено событие match.accepted: ' + JSON.stringify(data));
                
                // Проверяем, для нас ли это событие
                const currentUserId = {{ auth()->id() ?? 1671 }};
                const toCaptainId = parseInt(data.toCaptainId);
                
                if (toCaptainId === currentUserId) {
                    log('✅ Событие предназначено для текущего пользователя');
                    alert('Получено событие принятия матча!');
                } else {
                    log('ℹ️ Событие не для текущего пользователя');
                }
            });
        }
        
        function sendTestEvent() {
            const matchId = document.getElementById('matchId').value;
            const fromCaptainId = document.getElementById('fromCaptainId').value;
            const toCaptainId = document.getElementById('toCaptainId').value;
            
            log(`Отправляем тестовое событие: matchId=${matchId}, from=${fromCaptainId}, to=${toCaptainId}`);
            
            fetch('/test-event/match-accepted', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    matchId: matchId,
                    fromCaptainId: fromCaptainId,
                    toCaptainId: toCaptainId
                })
            })
            .then(response => response.json())
            .then(data => {
                log('📤 Ответ сервера: ' + JSON.stringify(data));
            })
            .catch(error => {
                log('🚨 Ошибка отправки: ' + error.message);
            });
        }
        
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            initPusher();
            
            document.getElementById('testForm').addEventListener('submit', function(e) {
                e.preventDefault();
                sendTestEvent();
            });
        });
    </script>
</body>
</html>
