<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

// Импортируем сервис
use App\Services\Interfaces\InvitationManagerInterface;

class ReceivedInvitationsList extends Component
{
    public array $receivedInvitations = [];

    protected InvitationManagerInterface $invitationService;

    protected $listeners = [
        'loadReceivedInvitations' => 'loadReceivedInvitations',
    ];

    public function boot(InvitationManagerInterface $invitationService)
    {
        $this->invitationService = $invitationService;
    }

    /**
     * Mount lifecycle hook. Загружаем полученные приглашения.
     */
    public function mount()
    {
        $this->loadReceivedInvitations();
    }

    /**
     * Слушатель события для обновления списка полученных приглашений.
     */
    #[On('accepted_invitation')] // Когда приглашение принято
    #[On('declined_invitation')] // Когда приглашение отклонено
    public function loadReceivedInvitations($data = null): void
    {
        if (Auth::check()) {
            // Принудительно создаём новый массив, чтобы Livewire увидел изменение
            $this->receivedInvitations = array_values(
                $this->invitationService->getReceivedInvitationsForPlayer(Auth::id())->toArray()
            );
        } else {
            $this->receivedInvitations = [];
        }
    }

    public function getListeners()
    {
        $userId = Auth::id();
        return [
            "echo:player.{$userId},invitation.sent" => 'onInvitationSent',
            // остальные слушатели Livewire
        ];
    }

    public function onInvitationSent($event)
    {
        $this->loadReceivedInvitations();
    }

    /**
     * Принимает приглашение в команду.
     */
    public function acceptInvitation(int $invitationId)
    {
        try {
            $this->invitationService->acceptInvitation($invitationId, Auth::id());

            // Обновляем список приглашений
            $this->loadReceivedInvitations();

            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Приглашение принято. Вы вступили в команду!'
            ]);
            $this->dispatch('accepted_invitation'); // Оповещаем другие компоненты об изменении
            $this->dispatch('refreshTeamList'); // Оповещаем TeamList, чтобы обновить данные команды
        } catch (\Exception $e) {
            Log::error('Ошибка при принятии приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Отклоняет приглашение в команду.
     */
    public function declineInvitation(int $invitationId)
    {
        try {
            $this->invitationService->declineInvitation($invitationId, Auth::id());

            // Обновляем список приглашений
            $this->loadReceivedInvitations();

            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Приглашение отклонено.'
            ]);
            $this->dispatch('declined_invitation'); // Оповещаем другие компоненты об изменении
        } catch (\Exception $e) {
            Log::error('Ошибка при отклонении приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка: ' . $e->getMessage()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.received-invitations-list');
    }
}